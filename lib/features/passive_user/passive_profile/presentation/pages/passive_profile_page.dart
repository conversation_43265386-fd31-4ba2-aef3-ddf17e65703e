import 'package:flipped_flutter/shared/widgets/common/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../injection_container.dart';
import '../bloc/passive_profile_bloc.dart';
import '../widgets/passive_profile_widget.dart';

class PassiveProfilePage extends StatelessWidget {
  const PassiveProfilePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Profile'),
      body: BlocProvider(
        create: (_) => sl<PassiveProfileBloc>(),
        child: const PassiveProfileWidget(),
      ),
    );
  }
}
