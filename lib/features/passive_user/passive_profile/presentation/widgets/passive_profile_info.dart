import 'package:flipped_flutter/features/active_user/active_profile/domain/entities/active_profile.dart';
import 'package:flipped_flutter/shared/widgets/media/custom_circle_avatar.dart';
import 'package:flutter/material.dart';
import '../../../../../shared/constants/app_values.dart';
import '../../../../active_user/active_profile/presentation/widgets/joining_text.dart';
import '../../domain/entities/passive_profile.dart';

class PassiveProfileInfo extends StatelessWidget {
  const PassiveProfileInfo({super.key, required this.profile});
  final PassiveProfile profile;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomCircleAvatar(name: profile.name),
        const SizedBox(width: AppValues.padding),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              profile.name,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Text(profile.email, style: Theme.of(context).textTheme.bodyMedium),
            MemberSinceText(dateString: profile.createdAt),
          ],
        ),
      ],
    );
  }
}
