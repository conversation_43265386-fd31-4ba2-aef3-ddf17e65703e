import 'package:flipped_flutter/features/passive_user/passive_profile/presentation/widgets/passive_profile_info_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../shared/constants/app_values.dart';
import '../../../../../shared/widgets/common/error_widget.dart';
import '../../../../active_user/active_profile/presentation/widgets/legal_support.dart';
import '../../../../active_user/active_profile/presentation/widgets/sign_out_button.dart';
import '../bloc/passive_profile_bloc.dart';

class PassiveProfileWidget extends StatefulWidget {
  const PassiveProfileWidget({Key? key}) : super(key: key);

  @override
  State<PassiveProfileWidget> createState() => _PassiveProfileWidgetState();
}

class _PassiveProfileWidgetState extends State<PassiveProfileWidget> {
  @override
  void initState() {
    super.initState();
    // Trigger the event to load data when the widget initializes
    if(context.read<PassiveProfileBloc>().state is PassiveProfileInitial) {
      context.read<PassiveProfileBloc>().add(GetPassiveProfileEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PassiveProfileBloc, PassiveProfileState>(
      builder: (context, state) {
        if (state is PassiveProfileInitial) {
          return const Center(
            child: Text('Press the button to load passive_profile'),
          );
        } else if (state is PassiveProfileLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is PassiveProfileLoaded) {
          List<Widget> widgetList = [
            PassiveProfileInfoCard(profile: state.passiveProfile),
            LegalSupport(),
            SignOutButton(),
          ];

          return ListView.separated(
           padding:const EdgeInsets.all(AppValues.padding),
            itemBuilder: (context, index) {
              return widgetList[index];
            },
            separatorBuilder: (context, index) {
              return const SizedBox(height: AppValues.height_10);
            },
            itemCount: widgetList.length,
          );
        } else if (state is PassiveProfileError) {
          return ErrorWidgetCustom(
            message: state.message,
            onRetry: () {
              context.read<PassiveProfileBloc>().add(GetPassiveProfileEvent());
            },
          );
        } else {
          return const Center(child: Text('Unknown state'));
        }
      },
    );
  }
}
