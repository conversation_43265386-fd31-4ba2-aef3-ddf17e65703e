import 'package:equatable/equatable.dart';

class PassiveProfile extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? emailVerifiedAt;
  final String? phone;
  final String? phoneVerifiedAt;
  final String type;
  final dynamic settings; // Can be updated to a proper model later
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  const PassiveProfile({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    required this.type,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    email,
    emailVerifiedAt,
    phone,
    phoneVerifiedAt,
    type,
    settings,
    createdAt,
    updatedAt,
    deletedAt,
  ];
}
