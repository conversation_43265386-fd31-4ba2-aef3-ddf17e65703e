import 'package:flipped_flutter/shared/widgets/common/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../injection_container.dart';
import '../bloc/portfolio_bloc.dart';
import '../widgets/portfolio_widget.dart';

class PortfolioPage extends StatelessWidget {
  const PortfolioPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Portfolio'),
      body: BlocProvider(
        create: (_) => sl<PortfolioBloc>(),
        child: const PortfolioWidget(),
      ),
    );
  }
}
