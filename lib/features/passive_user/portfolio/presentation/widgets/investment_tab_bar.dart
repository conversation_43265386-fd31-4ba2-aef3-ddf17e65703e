import 'package:flutter/material.dart';

class InvestmentTabBar extends StatefulWidget {
  final int activeCount;
  final int completedCount;

  const InvestmentTabBar({
    super.key,
    required this.activeCount,
    required this.completedCount,
  });

  @override
  State<InvestmentTabBar> createState() => _InvestmentTabBarState();
}

class _InvestmentTabBarState extends State<InvestmentTabBar> {
  bool isActiveSelected = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          _buildTab('Active (${widget.activeCount})', isActiveSelected),
          _buildTab('Completed (${widget.completedCount})', !isActiveSelected),
        ],
      ),
    );
  }

  Expanded _buildTab(String label, bool isSelected) {
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => isActiveSelected = label.startsWith('Active')),
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? Colors.white : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          alignment: Alignment.center,
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: isSelected ? Colors.black87 : Colors.black54,
            ),
          ),
        ),
      ),
    );
  }
}