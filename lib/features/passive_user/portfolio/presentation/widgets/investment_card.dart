import 'package:flutter/material.dart';

class InvestmentCard extends StatelessWidget {
  final String title;
  final String location;
  final String investmentAmount;
  final String managerName;
  final String phase;
  final double progressPercent;
  final String nextTask;
  final String imagePath;

  const InvestmentCard({
    super.key,
    required this.title,
    required this.location,
    required this.investmentAmount,
    required this.managerName,
    required this.phase,
    required this.progressPercent,
    required this.nextTask,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: Image.network(imagePath, height: 180, width: double.infinity, fit: BoxFit.cover),
          ),

          // Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title + Phase
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        phase,
                        style: const TextStyle(fontSize: 12, color: Colors.blue),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(location, style: const TextStyle(color: Colors.black54, fontSize: 13)),

                const SizedBox(height: 12),

                // Investment and Manager
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Investment\n$investmentAmount',
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                    Text('Manager\n$managerName',
                        textAlign: TextAlign.right,
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                  ],
                ),

                const SizedBox(height: 12),

                // Progress bar
                const Text('Progress', style: TextStyle(color: Colors.black54, fontSize: 13)),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: progressPercent / 100,
                  minHeight: 6,
                  borderRadius: BorderRadius.circular(4),
                  backgroundColor: Colors.grey.shade300,
                  color: Colors.teal,
                ),

                const SizedBox(height: 6),
                Text('Next: $nextTask', style: const TextStyle(color: Colors.black54)),

                const SizedBox(height: 12),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {},
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                        child: const Text('View Details'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.chat_bubble_outline),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}