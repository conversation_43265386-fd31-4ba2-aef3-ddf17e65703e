import 'package:flipped_flutter/features/passive_user/portfolio/presentation/widgets/portfolio_stat_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/portfolio_bloc.dart';
import 'investment_card.dart';
import 'investment_tab_bar.dart';

class PortfolioWidget extends StatefulWidget {
  const PortfolioWidget({Key? key}) : super(key: key);

  @override
  State<PortfolioWidget> createState() => _PortfolioWidgetState();
}

class _PortfolioWidgetState extends State<PortfolioWidget> {
  @override
  void initState() {
    super.initState();
    // Trigger the event to load data when the widget initializes
    if(context.read<PortfolioBloc>().state is PortfolioInitial) {
      context.read<PortfolioBloc>().add(GetPortfolioEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PortfolioBloc, PortfolioState>(
      builder: (context, state) {
        if (state is PortfolioInitial) {
          return const Center(
            child: Text('Press the button to load portfolio'),
          );
        } else if (state is PortfolioLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is PortfolioLoaded) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Summary Cards
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    PortfolioStatCard(
                      title: 'Total Invested',
                      value: '\$245K',
                      icon: Icons.attach_money,
                      bgColor: Color(0xFFE0FAFA),
                    ),
                    PortfolioStatCard(
                      title: 'Total Returns',
                      value: '\$52K',
                      icon: Icons.trending_up,
                      bgColor: Color(0xFFE0FAFA),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Tab Bar
                const InvestmentTabBar(activeCount: 3, completedCount: 2),

                const SizedBox(height: 16),

                // Investment Card
                const InvestmentCard(
                  title: 'Victorian Renovation - Melbourne',
                  location: 'Richmond, VIC',
                  investmentAmount: '\$45,000',
                  managerName: 'Sarah Chen',
                  phase: 'Renovation Phase',
                  progressPercent: 65,
                  nextTask: 'Kitchen Installation',
                  imagePath: 'https://picsum.photos/400/200',
                ),
              ],
            ),
          );
        } else if (state is PortfolioError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error: ${state.message}',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.red,
                      ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<PortfolioBloc>().add(GetPortfolioEvent());
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        } else {
          return const Center(
            child: Text('Unknown state'),
          );
        }
      },
    );
  }
}