import 'dart:convert';
import 'package:flipped_flutter/core/network/dio_client.dart';
import 'package:http/http.dart' as http;
import '../models/portfolio_model.dart';

abstract class PortfolioRemoteDataSource {
  Future<PortfolioModel> getPortfolio();
}

class PortfolioRemoteDataSourceImpl implements PortfolioRemoteDataSource {
  final DioClient networkClient;

  PortfolioRemoteDataSourceImpl({required this.networkClient});

  @override
  Future<PortfolioModel> getPortfolio() async {
    final response = await networkClient.client.get(
      'https://wp.aminul.work/v1',
    );

    if (response.statusCode == 200) {
      return PortfolioModel(id: "id",title: 'test');
    } else {
      throw Exception('Failed to load portfolio');
    }
  }
}