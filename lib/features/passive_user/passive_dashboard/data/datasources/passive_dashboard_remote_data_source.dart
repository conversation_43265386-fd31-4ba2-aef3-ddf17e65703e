import 'dart:convert';
import 'package:flipped_flutter/core/network/dio_client.dart';
import 'package:http/http.dart' as http;
import '../models/passive_dashboard_model.dart';

abstract class PassiveDashboardRemoteDataSource {
  Future<PassiveDashboardModel> getPassiveDashboard();
}

class PassiveDashboardRemoteDataSourceImpl implements PassiveDashboardRemoteDataSource {
  final DioClient networkClient;

  PassiveDashboardRemoteDataSourceImpl({required this.networkClient});

  @override
  Future<PassiveDashboardModel> getPassiveDashboard() async {
    final response = await networkClient.client.get('https://jsonplaceholder.typicode.com/todos/1'
    );

    if (response.statusCode == 200) {
      return PassiveDashboardModel(id: '3',title: '3jj');
    } else {
      throw Exception('Failed to load passive_dashboard');
    }
  }
}