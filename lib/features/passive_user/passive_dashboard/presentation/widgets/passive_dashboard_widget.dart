import 'package:flipped_flutter/core/navigation/routing/navigation_helper.dart';
import 'package:flipped_flutter/features/passive_user/passive_dashboard/presentation/widgets/quick_access_button.dart';
import 'package:flipped_flutter/features/passive_user/passive_dashboard/presentation/widgets/stat_card.dart';
import 'package:flipped_flutter/shared/widgets/common/error_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/passive_dashboard_bloc.dart';
import 'finance_readiness_card.dart';
import 'learning_progress_card.dart';

class PassiveDashboardWidget extends StatefulWidget {
  const PassiveDashboardWidget({Key? key}) : super(key: key);

  @override
  State<PassiveDashboardWidget> createState() => _PassiveDashboardWidgetState();
}

class _PassiveDashboardWidgetState extends State<PassiveDashboardWidget> {
  @override
  void initState() {
    super.initState();
    // Trigger the event to load data when the widget initializes
    if(context.read<PassiveDashboardBloc>().state is PassiveDashboardInitial) {
      context.read<PassiveDashboardBloc>().add(GetPassiveDashboardEvent());
    }

  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PassiveDashboardBloc, PassiveDashboardState>(
      builder: (context, state) {
        if (state is PassiveDashboardInitial) {
          return const Center(
            child: Text('Press the button to load passive_dashboard'),
          );
        } else if (state is PassiveDashboardLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is PassiveDashboardLoaded) {
          return
            SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Portfolio Overview',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A1A1A),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Stats Grid
                  Row(
                    children: [
                      Expanded(
                        child: StatCard(
                          icon: Icons.attach_money,
                          iconColor: const Color(0xFF0A5F6F),
                          label: 'Invested',
                          value: '\$245K',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: StatCard(
                          icon: Icons.trending_up,
                          iconColor: const Color(0xFF00C853),
                          label: 'Returned',
                          value: '\$52K',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: StatCard(
                          icon: Icons.pie_chart,
                          iconColor: const Color(0xFF4DD0E1),
                          label: 'Active Deals',
                          value: '4',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: StatCard(
                          icon: Icons.show_chart,
                          iconColor: const Color(0xFFFFA000),
                          label: 'Avg ROI',
                          value: '18.2%',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Finance Readiness Card
                  const FinanceReadinessCard(),
                  const SizedBox(height: 32),

                  // Quick Access
                  const Text(
                    'Quick Access',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A1A1A),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: QuickAccessButton(
                          icon: Icons.trending_up,
                          iconColor: const Color(0xFF0A5F6F),
                          label: 'New Deals',
                          onTap: () {},
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: QuickAccessButton(
                          icon: Icons.pie_chart,
                          iconColor: const Color(0xFF4DD0E1),
                          label: 'Portfolio',
                          onTap: () {},
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: QuickAccessButton(
                          icon: Icons.notifications,
                          iconColor: const Color(0xFFFFA000),
                          label: 'Notifications',
                          hasNotification: true,
                          onTap: () {},
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: QuickAccessButton(
                          icon: Icons.calculate,
                          iconColor: const Color(0xFF00C853),
                          label: 'ROI Simulator',
                          onTap: () {
                            NavigationHelper.goToROICalculator(context);
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Learning Progress
                  const LearningProgressCard(),
                ],
              ),
            );
        } else if (state is PassiveDashboardError) {
          return ErrorWidgetCustom(
            message: state.message,
            onRetry: () {
              context.read<PassiveDashboardBloc>().add(GetPassiveDashboardEvent());
            },
          );
        } else {
          return const Center(
            child: Text('Unknown state'),
          );
        }
      },
    );
  }
}