import 'package:flipped_flutter/shared/widgets/common/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../injection_container.dart';
import '../bloc/chat_bloc.dart';
import '../widgets/chat_widget.dart';

class ChatPage extends StatelessWidget {
  const ChatPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: "Chat"),
      body: BlocProvider(
        create: (_) => sl<ChatBloc>(),
        child: const ChatWidget(),
      ),
    );
  }
}