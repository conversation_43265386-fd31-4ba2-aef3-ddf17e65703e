import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';

import '../../../../shared/widgets/media/custom_circle_avatar.dart';

class ChatListCard extends StatelessWidget {
  const ChatListCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(child: ListTile(
      leading: CustomCircleAvatar(name: '<PERSON>',),
      title: Text('<PERSON>'),
      subtitle:Text('Hello, how are you?'),
      trailing: Text('2:30 PM'),
    ));
  }
}
