import 'package:flipped_flutter/shared/constants/app_colors.dart';
import 'package:flutter/material.dart';

class ChatSearchTextField extends StatelessWidget {
  const ChatSearchTextField({super.key});

  @override
  Widget build(BuildContext context) {
    return TextField(

      decoration: InputDecoration(
        hintText: 'Search conversations',
        prefixIcon: Icon(Icons.search),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color:  Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.colorPrimary, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.colorPrimary),
        ),
  
        filled: true,
        contentPadding: const EdgeInsets.all(16),
      ),
    );
  }
}
