import 'package:flipped_flutter/features/chat/presentation/widgets/chat_list_card.dart';
import 'package:flipped_flutter/features/chat/presentation/widgets/chat_overview.dart';
import 'package:flipped_flutter/features/chat/presentation/widgets/chat_search_text_field.dart';
import 'package:flipped_flutter/features/chat/presentation/widgets/quick_action_section.dart';
import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flipped_flutter/shared/widgets/common/error_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/chat_bloc.dart';
import 'chat_filter_section.dart';

class ChatWidget extends StatefulWidget {
  const ChatWidget({Key? key}) : super(key: key);

  @override
  State<ChatWidget> createState() => _ChatWidgetState();
}

class _ChatWidgetState extends State<ChatWidget> {
  @override
  void initState() {
    super.initState();
    if(context.read<ChatBloc>().state is ChatInitial) {
      context.read<ChatBloc>().add(GetChatEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        if (state is ChatInitial) {
          return const Center(
            child: Text('Press the button to load chat'),
          );
        } else if (state is ChatLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is ChatLoaded) {
          return Padding(
            padding: const EdgeInsets.all(AppValues.padding),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 10),
                  ChatSearchTextField(),
                  const SizedBox(height: 10),
                  ChatFilterSection(),
                  const SizedBox(height: 10),
                  ChatOverview(),
                  const SizedBox(height: 10),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      return ChatListCard();
                    },
                  ),
                  const SizedBox(height: 10),
                  QuickActionSection(),
                ],
              ),
            ),
          );
        } else if (state is ChatError) {
          return ErrorWidgetCustom(message: state.message);
        } else {
          return const Center(
            child: Text('Unknown state'),
          );
        }
      },
    );
  }
}