import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flipped_flutter/shared/utils/styles/app_text_styles.dart';
import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';

class QuickActionSection extends StatelessWidget {
  const QuickActionSection({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      padding: const EdgeInsets.all(AppValues.halfPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Quick Actions", style: AppTextStyles.heading3(context)),
          SizedBox(height: AppValues.height_10),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              ElevatedContainer(
                padding: EdgeInsets.all(AppValues.halfPadding),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: AppValues.iconSize_18,
                      backgroundColor: Theme.of(
                        context,
                      ).colorScheme.primaryContainer,
                      child: Icon(
                        Icons.message,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                    SizedBox(height: AppValues.height_5),
                    Text("New Deal Room", style: AppTextStyles.body1(context)),
                  ],
                ),
              ),
              ElevatedContainer(
                padding: EdgeInsets.all(AppValues.halfPadding),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: AppValues.iconSize_18,
                      backgroundColor: Theme.of(
                        context,
                      ).colorScheme.primaryContainer,
                      child: Icon(
                        Icons.group_add,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                    SizedBox(height: AppValues.height_5),
                    Text(
                      "Broadcast Message",
                      style: AppTextStyles.body1(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
