import 'package:flipped_flutter/shared/utils/styles/app_text_styles.dart';
import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/cupertino.dart';

class ChatOverview extends StatelessWidget {
  const ChatOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      const OverviewCard(),
      const SizedBox(width: 16,),
      const OverviewCard(),
      const SizedBox(width: 16,),
      const OverviewCard(),
    ],);
  }
}

class OverviewCard extends StatelessWidget {
  const OverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(

      child: <PERSON><PERSON><PERSON><PERSON>(
        height: 60,
        width: 100,
        child: Column(
        children: [
          Text('3',style: AppTextStyles.highlightBoldNumber(context),),
          Text('Unread'),
        ],
            ),
      ),);
  }
}
