import 'package:flutter/material.dart';

class ChatFilterSection extends StatelessWidget {
  const ChatFilterSection({super.key});

  @override
  Widget build(BuildContext context) {
    const list=['All','Unread','Deal Rooms', 'investors'];
    return   Wrap(
      spacing: 5.0,
      children: List<Widget>.generate(4, (int index) {
        return ChoiceChip(
          label: Text('${list[index]}'),
          selected:true,
          onSelected: (bool selected) {
            //TODO: implement filter logic
          },
        );
      }).toList(),
    );
  }
}
