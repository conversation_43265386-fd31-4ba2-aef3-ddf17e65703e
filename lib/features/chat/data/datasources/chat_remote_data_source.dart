import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flipped_flutter/core/network/dio_client.dart';
import 'package:http/http.dart' as http;
import '../../../../config/app_config.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/model/api_response.dart';
import '../models/chat_model.dart';

abstract class ChatRemoteDataSource {
  Future<ChatModel> getChat();
}

class ChatRemoteDataSourceImpl implements ChatRemoteDataSource {
  final DioClient networkClient;

  ChatRemoteDataSourceImpl({required this.networkClient});

  @override
  Future<ChatModel> getChat() async {
    try{
      final response = await networkClient.client.get(
        'https://wp.aminul.work/v1',
      );

      // final profileResponse = _handleApiResponse<ChatModel>(
      //   response,
      //       (data) => ChatModel.fromJson(data),
      //   'Chat load failed',
      // );
      return ChatModel(id: 'id-212', title: 'Sample Chat');
    }on DioException catch (e) {
      throw _handleDioError(e);
    }
  }



  /// Helper method to handle API responses consistently
  T _handleApiResponse<T>(
      Response response,
      T Function(dynamic) fromJson,
      String errorMessage,
      ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      final apiResponse = ApiResponse<T>.fromJson(response.data, fromJson);

      if (apiResponse.isSuccess && apiResponse.hasData) {
        return apiResponse.data!;
      } else if (apiResponse.isSuccess && !apiResponse.hasData) {
        throw ServerException(
          'Expected data in response but received none',
          statusCode: response.statusCode,
        );
      } else {
        throw ServerException(
          apiResponse.message.isNotEmpty ? apiResponse.message : errorMessage,
          statusCode: response.statusCode,
        );
      }
    } else {
      throw ServerException(errorMessage, statusCode: response.statusCode);
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException('Connection timeout');
      case DioExceptionType.connectionError:
        return NetworkException('No internet connection');
      case DioExceptionType.badResponse:
        return ServerException(
          e.response?.data['message'] ?? 'Server error',
          statusCode: e.response?.statusCode,
        );

      default:
        return ServerException('Unexpected error occurred');
    }
  }



}