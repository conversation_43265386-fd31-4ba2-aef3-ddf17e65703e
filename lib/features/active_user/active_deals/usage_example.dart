// Example of how to use the GetActiveDealsParams in your UI
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'domain/usecases/get_active_deals_usecase.dart';
import 'presentation/bloc/active_deals_bloc.dart';

class ActiveDealsUsageExample extends StatelessWidget {
  const ActiveDealsUsageExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Active Deals')),
      body: Column(
        children: [
          // Example buttons to trigger different API calls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () {
                  // Load first page with default parameters
                  context.read<ActiveDealsBloc>().add(
                    const GetActiveDealsEvent(), // Uses default params (page: 1, perPage: 10)
                  );
                },
                child: const Text('Load Default'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Load second page
                  context.read<ActiveDealsBloc>().add(
                    GetActiveDealsEvent(
                      params: const GetActiveDealsParams(page: 2, perPage: 10),
                    ),
                  );
                },
                child: const Text('Page 2'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Load with more items per page
                  context.read<ActiveDealsBloc>().add(
                    GetActiveDealsEvent(
                      params: const GetActiveDealsParams(page: 1, perPage: 20),
                    ),
                  );
                },
                child: const Text('20 per page'),
              ),
            ],
          ),
          
          // BLoC Consumer to display the results
          Expanded(
            child: BlocBuilder<ActiveDealsBloc, ActiveDealsState>(
              builder: (context, state) {
                if (state is ActiveDealsLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ActiveDealsLoaded) {
                  return ListView.builder(
                    itemCount: state.activeDeals.length,
                    itemBuilder: (context, index) {
                      final deal = state.activeDeals[index];
                      return ListTile(
                        title: Text(deal.title),
                        subtitle: Text('Status: ${deal.status}'),
                        trailing: Text('ID: ${deal.id}'),
                      );
                    },
                  );
                } else if (state is ActiveDealsError) {
                  return Center(child: Text('Error: ${state.message}'));
                }
                return const Center(child: Text('Press a button to load deals'));
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Example of how to create custom parameters for specific use cases
class ActiveDealsParameterExamples {
  
  // Load first page with default settings
  static GetActiveDealsParams firstPage() {
    return GetActiveDealsParams.defaultParams();
  }
  
  // Load next page
  static GetActiveDealsParams nextPage(int currentPage) {
    return GetActiveDealsParams(page: currentPage + 1, perPage: 10);
  }
  
  // Load with custom page size
  static GetActiveDealsParams customPageSize(int pageSize) {
    return GetActiveDealsParams(page: 1, perPage: pageSize);
  }
  
  // Example of how you might extend this in the future
  // When you add more parameters to GetActiveDealsParams, you can use them like this:
  /*
  static GetActiveDealsParams withFilters({
    int page = 1,
    int perPage = 10,
    String? status,
    String? type,
  }) {
    return GetActiveDealsParams(
      page: page,
      perPage: perPage,
      // status: status,     // Add these when you extend the class
      // type: type,         // Add these when you extend the class
    );
  }
  */
}
