import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../../core/error/failures.dart';
import '../../../../../core/usecases/usecase.dart';
import '../../domain/entities/active_deals.dart';
import '../../domain/usecases/get_active_deals_usecase.dart';

part 'active_deals_event.dart';
part 'active_deals_state.dart';

const String SERVER_FAILURE_MESSAGE = 'Server Failure';
const String CACHE_FAILURE_MESSAGE = 'Cache Failure';

class ActiveDealsBloc extends Bloc<ActiveDealsEvent, ActiveDealsState> {
  final GetActiveDealsUseCase getActiveDealsUseCase;

  ActiveDealsBloc({required this.getActiveDealsUseCase})
    : super(ActiveDealsInitial()) {
    on<GetActiveDealsEvent>(_onGetActiveDealsEvent);
  }

  void _onGetActiveDealsEvent(
    GetActiveDealsEvent event,
    Emitter<ActiveDealsState> emit,
  ) async {
    emit(ActiveDealsLoading());
    final failureOrSuccess = await getActiveDealsUseCase(event.params);
    emit(_eitherLoadedOrErrorState(failureOrSuccess));
  }

  ActiveDealsState _eitherLoadedOrErrorState(
    Either<Failure, List<ActiveDeals>> either,
  ) {
    return either.fold(
      (failure) => ActiveDealsError(message: _mapFailureToMessage(failure)),
      (activeDeals) => ActiveDealsLoaded(activeDeals: activeDeals),
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return SERVER_FAILURE_MESSAGE;
      case CacheFailure:
        return CACHE_FAILURE_MESSAGE;
      default:
        return 'Unexpected Error';
    }
  }
}
