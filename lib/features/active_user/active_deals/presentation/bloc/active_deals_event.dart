part of 'active_deals_bloc.dart';

abstract class ActiveDealsEvent extends Equatable {
  const ActiveDealsEvent();

  @override
  List<Object> get props => [];
}

class GetActiveDealsEvent extends ActiveDealsEvent {
  final GetActiveDealsParams params;

  const GetActiveDealsEvent({GetActiveDealsParams? params})
      : params = params ?? const GetActiveDealsParams();

  @override
  List<Object> get props => [params];
}