part of 'active_deals_bloc.dart';

abstract class ActiveDealsState extends Equatable {
  const ActiveDealsState();

  @override
  List<Object> get props => [];
}

class ActiveDealsInitial extends ActiveDealsState {}

class ActiveDealsLoading extends ActiveDealsState {}

class ActiveDealsLoaded extends ActiveDealsState {
  final List<ActiveDeals> activeDeals;

  const ActiveDealsLoaded({required this.activeDeals});

  @override
  List<Object> get props => [activeDeals];
}

class ActiveDealsError extends ActiveDealsState {
  final String message;

  const ActiveDealsError({required this.message});

  @override
  List<Object> get props => [message];
}