import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/common/custom_app_bar.dart';
import '../bloc/active_deals_bloc.dart';
import 'package:flutter/cupertino.dart';

import '../widgets/active_deals_widget.dart';

class ActiveDealsPage extends StatefulWidget {
  const ActiveDealsPage({super.key});

  @override
  State<ActiveDealsPage> createState() => _ActiveDealsPageState();
}

class _ActiveDealsPageState extends State<ActiveDealsPage> {
  List<Tab> tabs = const [
    Tab(text: "All"),
    Tab(text: "Submitted"),
    Tab(text: "Approved"),
    Tab(text: "Active"),
    Tab(text: "Rejected"),
  ];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Deals',
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications),
              color: Colors.grey[800],
              onPressed: () {
                // Handle filter action
              },
            ),
          ],
        ),
        body: BlocProvider(
          create: (_) => sl<ActiveDealsBloc>(),
          child: const ActiveDealsWidget(),
        ),
      ),
    );
  }
}
