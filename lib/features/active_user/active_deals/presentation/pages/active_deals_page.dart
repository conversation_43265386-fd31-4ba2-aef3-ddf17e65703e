import 'package:flutter/material.dart';
import '../../../../../shared/constants/app_values.dart';
import '../../../../../shared/widgets/common/custom_app_bar.dart';
import '../../data/models/temp_active_deal_model.dart';
import '../widgets/active_deal_card.dart';
import 'package:flutter/cupertino.dart';

class ActiveDealsPage extends StatefulWidget {
  const ActiveDealsPage({super.key});

  @override
  State<ActiveDealsPage> createState() => _ActiveDealsPageState();
}

class _ActiveDealsPageState extends State<ActiveDealsPage> {
  List<Tab> tabs = const [
    Tab(text: "All"),
    Tab(text: "Submitted"),
    Tab(text: "Approved"),
    Tab(text: "Active"),
    Tab(text: "Rejected"),
  ];

  final deal1 = ActiveDealModel(
    address: "123 Main St, Springfield",
    status: "Active",
    dealType: "Buy",
    price: "\$250,000",
    roi: "8%",
    updatedAt: "5 hours ago",
  );
  final deal2 = ActiveDealModel(
    address: "456 Elm St, Shelbyville",
    status: "Approved",
    dealType: "Sell",
    price: "\$300,000",
    roi: "10%",
    updatedAt: "2 days ago",
  );
  final deal3 = ActiveDealModel(
    address: "789 Oak St, Capital City",
    status: "Rejected",
    dealType: "Rent",
    price: "\$1,500/month",
    roi: "5%",
    updatedAt: "1 week ago",
  );

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Deals',
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications),
              color: Colors.grey[800],
              onPressed: () {
                // Handle filter action
              },
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(AppValues.halfPadding),
          child: Column(
            children: [
              Row(
                children: [
                  const Expanded(
                    child: CupertinoSearchTextField(
                      placeholder: 'Search deals',
                      padding: EdgeInsets.all(AppValues.smallPadding + 2),
                      prefixIcon: Icon(Icons.search, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppValues.smallPadding,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(AppValues.radius_8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.filter_alt_outlined,
                          color: Colors.grey,
                        ),
                        DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: "Newest",
                            icon: const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey,
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: "Newest",
                                child: Text("Newest"),
                              ),
                              DropdownMenuItem(
                                value: "Oldest",
                                child: Text("Oldest"),
                              ),
                            ],
                            onChanged: (value) {},
                            style: const TextStyle(color: Colors.black),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Container(
                height: AppValues.height_40,
                padding: const EdgeInsets.all(AppValues.padding_4),
                margin: const EdgeInsets.all(AppValues.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(AppValues.radius_10),
                ),
                child: TabBar(
                  isScrollable: true,
                  tabs: tabs,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  labelColor: Colors.black,
                  unselectedLabelColor: Colors.black87,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppValues.radius - 1),
                    color: Colors.white,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                ),
              ),
              Expanded(
                child: TabBarView(
                  children: [
                    ActiveDealCard(deal: deal1),
                    ActiveDealCard(deal: deal2),
                    ActiveDealCard(deal: deal2),
                    ActiveDealCard(deal: deal1),
                    ActiveDealCard(deal: deal3),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
