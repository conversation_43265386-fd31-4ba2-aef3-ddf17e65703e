import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/buid_basic_info_form.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/buid_review_info.dart';
import 'package:flipped_flutter/shared/widgets/common/custom_app_bar.dart';
import 'package:flutter/material.dart';

import '../widgets/build_upload_form.dart';

class SubmitDealPage extends StatefulWidget {
  const SubmitDealPage({super.key});

  @override
  State<SubmitDealPage> createState() => _SubmitDealPageState();
}

class _SubmitDealPageState extends State<SubmitDealPage> {
  int _currentStep = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'Submit New Deal'),
      body: Column(
        children: [
          _buildStepHeader(),
          Expanded(
            child: IndexedStack(
              index: _currentStep,
              children: const [
                BuidBasicInfoForm(),
                BuildUploadForm(),
                BuildReviewInfo(),
              ],
            ),
          ),
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildStepHeader() {
    final steps = ["Basic Info", "Uploads", "Review"];

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          for (int i = 0; i < steps.length; i++) ...[
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: i <= _currentStep
                      ? Colors.teal
                      : Colors.grey.shade400,
                  child: Text(
                    "${i + 1}",
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  steps[i],
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: i <= _currentStep ? Colors.teal : Colors.grey,
                  ),
                ),
              ],
            ),

            // Connector line (skip after last step)
            if (i != steps.length - 1)
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  height: 2,
                  color: i < _currentStep ? Colors.teal : Colors.grey.shade300,
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: ElevatedButton(
        onPressed: () {
          if (_currentStep < 2) {
            setState(() => _currentStep += 1);
          }
        },
        style: ElevatedButton.styleFrom(minimumSize: const Size.fromHeight(50)),
        child: Text(_currentStep < 2 ? "Next: Upload Documents" : "Submit"),
      ),
    );
  }
}
