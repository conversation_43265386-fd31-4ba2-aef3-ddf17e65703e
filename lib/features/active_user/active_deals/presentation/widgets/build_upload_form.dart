import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/optional_document_field.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/required_documents_card.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/upload_details_card.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/uploaded_file_card.dart';
import 'package:flutter/material.dart';

class BuildUploadForm extends StatelessWidget {
  const BuildUploadForm({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          UploadDetailsCard(),
          UploadedFileCard(),
          RequiredDocumentsCard(),
          OptionalDocumentField(),
        ],
      ),
    );
  }
}