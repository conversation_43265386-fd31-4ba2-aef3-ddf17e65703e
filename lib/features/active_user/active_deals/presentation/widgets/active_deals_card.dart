import 'package:flutter/material.dart';

import '../../../../../shared/constants/app_values.dart';
import '../../data/models/active_deals_model.dart';

class ActiveDealCard extends StatelessWidget {
  const ActiveDealCard({super.key, required this.deal});

  final ActiveDealsModel deal;

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return Colors.blue;
      case "approved":
        return Colors.green;
      case "rejected":
        return Colors.red;
      case "pending":
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusBackgroundColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return Colors.blue.shade50;
      case "approved":
        return Colors.green.shade50;
      case "rejected":
        return Colors.red.shade50;
      case "pending":
        return Colors.orange.shade50;
      default:
        return Colors.grey.shade200;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return "${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return dateString;
    }
  }

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor(deal.status);
    final statusBgColor = _getStatusBackgroundColor(deal.status);

    return Card(
      elevation: AppValues.extraSmallElevation - 2,
      color: Colors.white,
      margin: const EdgeInsets.symmetric(vertical: AppValues.halfPadding),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppValues.radius_10),
      ),
      child: InkWell(
        onTap: () {
          // Handle card tap - navigate to detail page
          // Navigator.push(context, MaterialPageRoute(builder: (_) => DealDetailPage(dealId: deal.id)));
        },
        child: Padding(
          padding: const EdgeInsets.all(AppValues.largePadding - 4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and Status Row
              Row(
                children: [
                  Expanded(
                    child: Text(
                      deal.title,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: AppValues.width_10),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppValues.smallPadding,
                      vertical: AppValues.padding_4,
                    ),
                    decoration: BoxDecoration(
                      color: statusBgColor,
                      borderRadius: BorderRadius.circular(
                        AppValues.smallRadius,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timelapse_outlined,
                          size: AppValues.iconSize_14,
                          color: statusColor,
                        ),
                        const SizedBox(width: AppValues.width_10 - 4),
                        Text(
                          deal.status,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              if (deal.description != null && deal.description!.isNotEmpty) ...[
                const SizedBox(height: AppValues.height_14 - 6),
                Text(
                  deal.description!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: AppValues.height_14),

              // Deal Type and ID
              Text(
                "Deal Type: ${deal.type} ◉ ID: ${deal.id}",
                style: Theme.of(context).textTheme.bodySmall,
              ),

              const SizedBox(height: AppValues.height_14 - 6),

              // Active and Passive User Info
              Row(
                children: [
                  if (deal.active != null) ...[
                    Icon(
                      Icons.person_outline,
                      size: AppValues.iconSize_14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      "Active: ${deal.active!.name}",
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(width: AppValues.width_16),
                  ],
                  Icon(
                    Icons.chat_bubble_outline,
                    size: AppValues.iconSize_14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    "${deal.messagesCount} messages",
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),

              const SizedBox(height: AppValues.height_14),

              // Updated At
              Text(
                "Updated at ${_formatDate(deal.updatedAt)}",
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
