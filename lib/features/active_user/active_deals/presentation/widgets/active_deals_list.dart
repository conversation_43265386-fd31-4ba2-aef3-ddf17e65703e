import 'package:flutter/material.dart';

import '../../../../../shared/constants/app_values.dart';
import '../../data/models/active_deals_model.dart';
import 'active_deal_card.dart';

class ActiveDealsList extends StatelessWidget {
  const ActiveDealsList({super.key, required this.deals});

  final List<ActiveDealsModel> deals;

  @override
  Widget build(BuildContext context) {
    if (deals.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No active deals found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: deals.length,
      padding: EdgeInsets.only(bottom: AppValues.padding),
      physics: const AlwaysScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final deal = deals[index];
        return ActiveDealCard(
          key: ValueKey('deal_${deal.id}'), // Add unique key for each item
          deal: deal,
        );
      },
    );
  }
}
