import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/custom_text_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../../shared/constants/app_values.dart';

class FinancialDetailsCard extends StatelessWidget {
  const FinancialDetailsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      padding: const EdgeInsets.all(AppValues.padding_20),
      margin: const EdgeInsets.all(AppValues.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_10),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Details',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 20, color: Colors.black),
          ),
          const SizedBox(height: AppValues.height_10),
          const Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: "Purchase Price*",
                  placeholder: "\$0.00",
                ),
              ),
              SizedBox(width: AppValues.width_10),
              Expanded(
                child: CustomTextField(
                  label: "Rehab Budget*",
                  placeholder: "\$0.00",
                ),
              ),
            ],
          ),
          const Row(
            children: [
              Expanded(
                child: CustomTextField(
                  label: "After Repair Value*",
                  placeholder: "\$0.00",
                ),
              ),
              SizedBox(width: AppValues.width_10),
              Expanded(
                child: CustomTextField(
                  label: "Estimated Rent*",
                  placeholder: "\$0.00",
                ),
              ),
            ],
          ),
          const CustomTextField(label: "Down Payment*", placeholder: "\$0.00"),
        ],
      ),
    );
  }
}
