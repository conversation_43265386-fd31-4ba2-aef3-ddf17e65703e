import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({super.key, required this.text, required this.onPressed, this.icon});
  final String text;
  final VoidCallback onPressed;
  final IconData? icon;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppValues.smallPadding),
      margin: const EdgeInsets.only(top: AppValues.smallPadding, bottom: AppValues.smallPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppValues.smallRadius),
        border: Border.all(color: Colors.teal),
      ),
      child: Center(child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, color: Colors.teal),
            const SizedBox(width: AppValues.width_5),
          ],
          
          Text(
            text,
            style: const TextStyle(
              color: Colors.teal,
              fontSize: AppValues.iconSmallSize,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      )),
    );
  }
}
