import '../../data/models/active_deals_model.dart';
import '../../domain/entities/active_deals.dart';

extension ActiveDealsExtension on ActiveDeals {
  ActiveDealsModel toModel() {
    return ActiveDealsModel(
      id: id,
      activeId: activeId,
      passiveId: passiveId,
      title: title,
      description: description,
      status: status,
      type: type,
      metadata: metadata,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
      messagesCount: messagesCount,
      active: active?.toModel(),
    );
  }
}

extension ActiveUserExtension on ActiveUser {
  ActiveUserModel toModel() {
    return ActiveUserModel(
      id: id,
      name: name,
      email: email,
      emailVerifiedAt: emailVerifiedAt,
      phone: phone,
      phoneVerifiedAt: phoneVerifiedAt,
      type: type,
      settings: settings,
      createdAt: createdAt,
      updatedAt: updatedAt,
      deletedAt: deletedAt,
    );
  }
}
