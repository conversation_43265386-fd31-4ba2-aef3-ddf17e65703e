import 'package:flipped_flutter/shared/widgets/common/error_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/active_deals_bloc.dart';

class ActiveDealsWidget extends StatefulWidget {
  const ActiveDealsWidget({Key? key}) : super(key: key);

  @override
  State<ActiveDealsWidget> createState() => _ActiveDealsWidgetState();
}

class _ActiveDealsWidgetState extends State<ActiveDealsWidget> {
  @override
  void initState() {
    super.initState();
    // Trigger the event to load data when the widget initializes
    context.read<ActiveDealsBloc>().add(GetActiveDealsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActiveDealsBloc, ActiveDealsState>(
      builder: (context, state) {
        if (state is ActiveDealsInitial) {
          return const Center(
            child: Text('Press the button to load active_deals'),
          );
        } else if (state is ActiveDealsLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is ActiveDealsLoaded) {
          return ListView.builder(
            itemCount: state.activeDeals.length,
            itemBuilder: (context, index) {
              return Text(state.activeDeals[index].title);
            },
          );
        } else if (state is ActiveDealsError) {
          return ErrorWidgetCustom(message: state.message);
        } else {
          return const Center(child: Text('Unknown state'));
        }
      },
    );
  }
}
