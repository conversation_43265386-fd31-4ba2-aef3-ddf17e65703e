import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flipped_flutter/shared/widgets/common/error_widget.dart';
import '../../../../../shared/constants/app_values.dart';
import '../../data/models/active_deals_model.dart';
import '../../domain/usecases/get_active_deals_usecase.dart';
import '../bloc/active_deals_bloc.dart';

import 'active_deals_extensions.dart';
import 'active_deals_list.dart';
import 'safe_active_deals_list.dart';

class ActiveDealsWidget extends StatefulWidget {
  const ActiveDealsWidget({Key? key}) : super(key: key);

  @override
  State<ActiveDealsWidget> createState() => _ActiveDealsWidgetState();
}

class _ActiveDealsWidgetState extends State<ActiveDealsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _sortFilter = "Newest";
  String _searchQuery = "";
  String? _statusFilter;

  final List<Tab> tabs = const [
    Tab(text: "All"),
    Tab(text: "Active"),
    Tab(text: "Pending"),
    Tab(text: "Approved"),
    Tab(text: "Rejected"),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabs.length, vsync: this);
    _loadDeals();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadDeals() {
    final params = GetActiveDealsParams();
    context.read<ActiveDealsBloc>().add(GetActiveDealsEvent(params: params));
  }

  void _onTabChanged(int index) {
    setState(() {
      switch (index) {
        case 0:
          _statusFilter = null; // All
          break;
        case 1:
          _statusFilter = "active";
          break;
        case 2:
          _statusFilter = "pending";
          break;
        case 3:
          _statusFilter = "approved";
          break;
        case 4:
          _statusFilter = "rejected";
          break;
      }
    });
    _loadDeals();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value;
    });
    // Add debounce if needed
    _loadDeals();
  }

  void _onSortChanged(String? value) {
    if (value != null) {
      setState(() {
        _sortFilter = value;
      });
      _loadDeals();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActiveDealsBloc, ActiveDealsState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(AppValues.halfPadding),
          child: Column(
            children: [
              // Search and Filter Row
              Row(
                children: [
                  Expanded(
                    child: CupertinoSearchTextField(
                      placeholder: 'Search deals',
                      padding: const EdgeInsets.all(AppValues.smallPadding + 2),
                      prefixIcon: const Icon(Icons.search, color: Colors.grey),
                      onChanged: _onSearchChanged,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppValues.smallPadding,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(AppValues.radius_8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.filter_alt_outlined,
                          color: Colors.grey,
                        ),
                        DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _sortFilter,
                            icon: const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey,
                            ),
                            items: const [
                              DropdownMenuItem(
                                value: "Newest",
                                child: Text("Newest"),
                              ),
                              DropdownMenuItem(
                                value: "Oldest",
                                child: Text("Oldest"),
                              ),
                            ],
                            onChanged: _onSortChanged,
                            style: const TextStyle(color: Colors.black),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Tab Bar
              Container(
                height: AppValues.height_40,
                padding: const EdgeInsets.all(AppValues.padding_4),
                margin: const EdgeInsets.all(AppValues.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(AppValues.radius_10),
                ),
                child: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  tabs: tabs,
                  tabAlignment: TabAlignment.start,
                  dividerColor: Colors.transparent,
                  labelColor: Colors.black,
                  unselectedLabelColor: Colors.black87,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppValues.radius - 1),
                    color: Colors.white,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  onTap: _onTabChanged,
                ),
              ),

              // Content Area
              Expanded(child: _buildContent(state)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContent(ActiveDealsState state) {
    if (state is ActiveDealsInitial || state is ActiveDealsLoading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state is ActiveDealsError) {
      return ErrorWidgetCustom(message: state.message);
    } else if (state is ActiveDealsLoaded) {
      if (state.activeDeals.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No deals found',
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        );
      }
      // Convert ActiveDeals entities to ActiveDealsModel for the UI
      final dealsModels = state.activeDeals
          .map((deal) => deal.toModel())
          .toList();
      return ActiveDealsList(deals: dealsModels);
    } else {
      return const Center(child: Text('Unknown state'));
    }
  }
}
