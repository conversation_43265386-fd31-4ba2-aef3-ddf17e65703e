import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';

class RequiredDocumentsCard extends StatelessWidget {
  const RequiredDocumentsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppValues.padding_20),
      margin: const EdgeInsets.all(AppValues.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Required Documents',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 20),
          ),
          const SizedBox(height: AppValues.height_10),
          _buildListTile(
            title: 'ID Proof',
            subtitle: 'Upload a valid government-issued ID.',
            color: Colors.green,
            context: context,
          ),
          const SizedBox(height: AppValues.height_10),
          _buildListTile(
            title: 'Address Proof',
            subtitle: 'Upload a recent utility bill or bank statement.',
            context: context,
          ),
          const SizedBox(height: AppValues.height_10),
          _buildListTile(
            title: 'Income Proof',
            subtitle: 'Upload your latest payslip or tax return.',
            context: context,
          ),
        ],
      ),
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    Color? color,
    required BuildContext context,
  }) {
    return Card(
      elevation: 1,
      shape: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppValues.radius_8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      child: ListTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: 16,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
        leading: Icon(Icons.check_circle, color: color ?? Colors.greenAccent),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ),
    );
  }
}
