import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';

class UploadedFileCard extends StatelessWidget {
  const UploadedFileCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppValues.radius_8),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppValues.padding_20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Uploaded Files',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontSize: 20),
            ),
            const SizedBox(height: AppValues.height_8),
            ListView.builder(
              itemCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: AppValues.extraSmallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.teal.shade50,
                    border: Border.all(color: Colors.grey.shade50),
                    borderRadius: BorderRadius.circular(AppValues.radius_6),
                  ),
                  child: ListTile(
                    leading: const Icon(
                      Icons.done_all_outlined,
                      color: Colors.greenAccent,
                    ),
                    title: Row(
                      children: [
                        Text(
                          'Document_${index + 1}.pdf',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(width: AppValues.width_8),
                        _buildStatusContainer(
                          status: 'required',
                          color: Colors.redAccent.shade100,
                        ),
                        const SizedBox(width: AppValues.width_8),
                        _buildStatusContainer(
                          status: 'Uploaded',
                          color: Colors.blue,
                        ),
                      ],
                    ),
                    subtitle: Text(
                      '1.4 MB',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusContainer({required String status, required Color color}) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppValues.halfPadding,
        vertical: AppValues.padding_4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppValues.radius_4),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
