import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';
import '../../data/models/active_deals_model.dart';

class ActiveDealCard extends StatelessWidget {
  const ActiveDealCard({super.key, required this.deal});
  final ActiveDealsModel deal;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppValues.extraSmallElevation - 2,
      color: Colors.white,
      margin: const EdgeInsets.symmetric(vertical: AppValues.halfPadding),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppValues.radius_10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppValues.largePadding - 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    deal.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: AppValues.width_10),
                Container(
                  margin: const EdgeInsets.only(left: AppValues.margin_8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppValues.smallPadding,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(deal.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(
                      AppValues.smallRadius,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getStatusIcon(deal.status),
                        size: AppValues.iconSize_14,
                        color: _getStatusColor(deal.status),
                      ),
                      const SizedBox(width: AppValues.width_10),
                      Text(
                        deal.status.toUpperCase(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getStatusColor(deal.status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppValues.height_14),

            if (deal.description != null) ..[
              Text(
                deal.description!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppValues.height_10),
            ],

            Text(
              "Deal Type: ${deal.type.toUpperCase()} ◉ DEAL-${deal.id.toString().padLeft(3, '0')}",
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: AppValues.height_10),

            Row(
              children: [
                Expanded(
                  child: Text(
                    "Messages: ${deal.messagesCount}",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (deal.metadata != null)
                  Text(
                    "Metadata: ${deal.metadata.toString()}",
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.lightGreen.shade400,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: AppValues.height_14),

            if (deal.active != null) ..[
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    "Active User: ${deal.active!.name}",
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppValues.height_10),
            ],

            Text(
              "Updated: ${_formatDate(deal.updatedAt)}",
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.blue;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Icons.play_circle_outline;
      case 'approved':
        return Icons.check_circle_outline;
      case 'rejected':
        return Icons.cancel_outlined;
      case 'pending':
        return Icons.schedule;
      default:
        return Icons.help_outline;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return "${date.day}/${date.month}/${date.year}";
    } catch (e) {
      return dateString;
    }
  }
}
