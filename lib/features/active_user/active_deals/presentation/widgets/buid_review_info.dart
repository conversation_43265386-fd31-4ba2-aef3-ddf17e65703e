import 'package:flutter/material.dart';

class BuildReviewInfo extends StatelessWidget {
  const BuildReviewInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Center(
          child: Text(
            'Review Information Here',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontSize: 24),
          ),
        ),
        Center(
          child: Text(
            'This is where the review details will be displayed.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 16),
          ),
        ),
      ],
    );
  }
}
