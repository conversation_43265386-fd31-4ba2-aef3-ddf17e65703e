import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/cupertino.dart';

class CustomTextField extends StatelessWidget {
  const CustomTextField({
    super.key,
    required this.label,
    required this.placeholder,
  });
  final String label;
  final String placeholder;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppValues.halfPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: AppValues.textSizeSmall + 4, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: AppValues.height_4 + 2),
          CupertinoSearchTextField(
            placeholder: placeholder,
            keyboardType: TextInputType.number,
            padding: const EdgeInsets.all(AppValues.padding_12),
            prefixIcon: const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
