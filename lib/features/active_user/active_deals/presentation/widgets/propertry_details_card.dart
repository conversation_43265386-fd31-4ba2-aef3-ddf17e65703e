import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/custom_text_field.dart';
import 'package:flutter/material.dart';

import '../../../../../shared/constants/app_values.dart';

class PropertyDetailsCard extends StatefulWidget {
  PropertyDetailsCard({super.key});

  @override
  State<PropertyDetailsCard> createState() => _PropertyDetailsCardState();
}

class _PropertyDetailsCardState extends State<PropertyDetailsCard> {
  final _types = <String>['Apartment', 'House', 'Townhouse', 'Studio', 'Land'];

  String? _selected;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.25,
      padding: const EdgeInsets.all(AppValues.padding_20),
      margin: const EdgeInsets.all(AppValues.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_10),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Property Details",
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 20),
          ),
          const CustomTextField(
            label: "Property Address*",
            placeholder: "⟟ Los Angeles, CA",
          ),
          const SizedBox(height: AppValues.height_10),
          DropdownButtonFormField<String>(
            initialValue: _selected,
            isExpanded: true,
            style: Theme.of(context).textTheme.bodyMedium,
            decoration: InputDecoration(
              fillColor: Colors.grey[200],
              filled: true,
              hintText: 'Select property type',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppValues.radius_8),
                borderSide: BorderSide.none,
              ),
              // You can also customize contentPadding to match your design
            ),
            items: _types
                .map((t) => DropdownMenuItem<String>(value: t, child: Text(t)))
                .toList(),
            onChanged: (val) => setState(() => _selected = val),
            validator: (val) => val == null ? 'Select a property type' : null,
            icon: const Icon(Icons.arrow_drop_down), // trailing chevron
          ),
        ],
      ),
    );
  }
}
