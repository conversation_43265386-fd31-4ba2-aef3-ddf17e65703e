import 'package:flutter/material.dart';
import '../../data/models/active_deals_model.dart';
import 'active_deals_list.dart';

/// A safe wrapper for ActiveDealsList that prevents rendering errors
class SafeActiveDealsList extends StatelessWidget {
  const SafeActiveDealsList({
    Key? key,
    required this.deals,
  }) : super(key: key);

  final List<ActiveDealsModel> deals;

  @override
  Widget build(BuildContext context) {
    // Add safety checks to prevent rendering errors
    if (deals.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No active deals found',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // Wrap in a container to ensure proper constraints
    return Container(
      constraints: const BoxConstraints(
        minHeight: 100,
      ),
      child: ActiveDealsList(deals: deals),
    );
  }
}
