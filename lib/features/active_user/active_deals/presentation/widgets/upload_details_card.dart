import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/custom_button.dart';
import 'package:flutter/material.dart';

import '../../../../../shared/constants/app_values.dart';

class UploadDetailsCard extends StatelessWidget {
  const UploadDetailsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppValues.padding_20),
      margin: const EdgeInsets.all(AppValues.smallPadding),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Upload Details',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 20),
          ),
          const SizedBox(height: AppValues.height_20),
          Container(
            padding: const EdgeInsets.all(AppValues.padding_12),
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppValues.radius_8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.cloud_upload, size: 50, color: Colors.grey.shade400),
                const SizedBox(height: AppValues.height_10),
                Text(
                  'Drag & Drop files here or click to upload',
                  style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                ),
                const SizedBox(height: AppValues.height_5),
                Text(
                  'Supported formats: JPG, PNG, PDF. Max size: 10MB',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                ),
                const SizedBox(height: AppValues.height_20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomButton(
                      text: 'Add Photos',
                      onPressed: () {},
                      icon: Icons.photo,
                    ),
                    const SizedBox(width: AppValues.width_10),
                    CustomButton(
                      icon: Icons.document_scanner,
                      text: 'Add Documents',
                      onPressed: () {},
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
