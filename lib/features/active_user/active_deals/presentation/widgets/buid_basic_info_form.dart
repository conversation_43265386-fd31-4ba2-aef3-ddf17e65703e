import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/description_card_deals.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/financial_details_card.dart';
import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/propertry_details_card.dart';
import 'package:flutter/material.dart';

class BuidBasicInfoForm extends StatelessWidget {
  const BuidBasicInfoForm({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(children: [
        PropertyDetailsCard(),
         const FinancialDetailsCard(),
         const DescriptionCardDeals(),
         ]),
    );
  }
}
