import 'package:flipped_flutter/features/active_user/active_deals/presentation/widgets/custom_text_field.dart';
import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';

class DescriptionCardDeals extends StatelessWidget {
  const DescriptionCardDeals({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppValues.padding_20),
      margin: const EdgeInsets.all(AppValues.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppValues.radius_10),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Description",
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 20),
          ),
          const SizedBox(height: AppValues.height_10),
          const CustomTextField(
            label: "Project Description*",
            placeholder:
                "Describe the property, renovation plan, and investment opportunity",
          ),
        ],
      ),
    );
  }
}
