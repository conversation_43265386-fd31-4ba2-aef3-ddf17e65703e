import 'package:equatable/equatable.dart';

class ActiveDeals extends Equatable {
  final int id;
  final int activeId;
  final int passiveId;
  final String title;
  final String? description;
  final String status;
  final String type;
  final Map<String, dynamic>? metadata;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;
  final int messagesCount;
  final ActiveUser? active;

  const ActiveDeals({
    required this.id,
    required this.activeId,
    required this.passiveId,
    required this.title,
    this.description,
    required this.status,
    required this.type,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.messagesCount,
    this.active,
  });

  @override
  List<Object?> get props => [
        id,
        activeId,
        passiveId,
        title,
        description,
        status,
        type,
        metadata,
        createdAt,
        updatedAt,
        deletedAt,
        messagesCount,
        active,
      ];
}

class ActiveUser extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? emailVerifiedAt;
  final String? phone;
  final String? phoneVerifiedAt;
  final String type;
  final Map<String, dynamic>? settings;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  const ActiveUser({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    required this.type,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        emailVerifiedAt,
        phone,
        phoneVerifiedAt,
        type,
        settings,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}