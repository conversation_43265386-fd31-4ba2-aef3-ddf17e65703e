import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../../core/error/failures.dart';
import '../../../../../core/usecases/usecase.dart';
import '../entities/active_deals.dart';
import '../repositories/active_deals_repository.dart';

class GetActiveDealsUseCase implements UseCase<List<ActiveDeals>, GetActiveDealsParams> {
  final ActiveDealsRepository repository;

  GetActiveDealsUseCase(this.repository);

  @override
  Future<Either<Failure, List<ActiveDeals>>> call(GetActiveDealsParams params) async {
    return await repository.getActiveDeals(params);
  }
}

class GetActiveDealsParams extends Equatable {
  final int page;
  final int perPage;
  // Add more parameters here as needed

  const GetActiveDealsParams({
    this.page = 1,
    this.perPage = 10,
  });

  // Convert to query parameters for API calls
  Map<String, dynamic> toQueryParams() {
    return {
      'page': page,
      'per_page': perPage,
    };
  }

  @override
  List<Object?> get props => [page, perPage];

  // Convenience method to create params with default values
  factory GetActiveDealsParams.defaultParams() {
    return const GetActiveDealsParams();
  }

  // Copy with method for easy parameter updates
  GetActiveDealsParams copyWith({
    int? page,
    int? perPage,
  }) {
    return GetActiveDealsParams(
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
    );
  }
}
