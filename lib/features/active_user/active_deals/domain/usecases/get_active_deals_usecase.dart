import 'package:dartz/dartz.dart';
import '../../../../../core/error/failures.dart';
import '../../../../../core/usecases/usecase.dart';
import '../entities/active_deals.dart';
import '../repositories/active_deals_repository.dart';

class GetActiveDealsUseCase implements UseCase<List<ActiveDeals>, NoParams> {
  final ActiveDealsRepository repository;

  GetActiveDealsUseCase(this.repository);

  @override
  Future<Either<Failure, List<ActiveDeals>>> call(NoParams params) async {
    return await repository.getActiveDeals();
  }
}
