// Test file to verify ActiveDeals JSON parsing
import 'dart:convert';
import 'data/models/active_deals_model.dart';

void testActiveDealsJsonParsing() {
  // Sample JSON response from your API
  const String jsonResponse = '''
  {
    "data": [
      {
        "id": 54,
        "active_id": 105,
        "passive_id": 3,
        "title": "My deal",
        "description": null,
        "status": "pending",
        "type": "regular",
        "metadata": {
          "example": "Value"
        },
        "created_at": "2025-10-09T00:22:15.000000Z",
        "updated_at": "2025-10-09T00:22:15.000000Z",
        "deleted_at": null,
        "messages_count": 0,
        "active": {
          "id": 105,
          "name": "shihab",
          "email": "<EMAIL>",
          "email_verified_at": null,
          "phone": null,
          "phone_verified_at": null,
          "type": "active",
          "settings": null,
          "created_at": "2025-10-01T18:11:49.000000Z",
          "updated_at": "2025-10-01T18:11:49.000000Z",
          "deleted_at": null
        }
      }
    ]
  }
  ''';

  try {
    // Parse the JSON
    final Map<String, dynamic> responseData = json.decode(jsonResponse);
    final List<dynamic> dataList = responseData['data'] as List<dynamic>;
    
    // Convert to ActiveDealsModel list
    final List<ActiveDealsModel> activeDeals = dataList
        .map((item) => ActiveDealsModel.fromJson(item as Map<String, dynamic>))
        .toList();
    
    print('✅ Successfully parsed ${activeDeals.length} active deals');
    print('First deal: ${activeDeals.first.title}');
    print('Active user: ${activeDeals.first.active?.name}');
    print('Status: ${activeDeals.first.status}');
    
  } catch (e) {
    print('❌ Error parsing JSON: $e');
  }
}
