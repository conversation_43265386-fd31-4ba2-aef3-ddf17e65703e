import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? emailVerifiedAt;
  final String? phone;
  final String? phoneVerifiedAt;
  final String type;
  final Map<String, dynamic>? settings;
  final String createdAt;
  final String updatedAt;
  final String? deletedAt;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    required this.type,
    this.settings,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      emailVerifiedAt: json['email_verified_at'] as String?,
      phone: json['phone'] as String?,
      phoneVerifiedAt: json['phone_verified_at'] as String?,
      type: json['type'] as String,
      settings: json['settings'] as Map<String, dynamic>?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      deletedAt: json['deleted_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'email_verified_at': emailVerifiedAt,
      'phone': phone,
      'phone_verified_at': phoneVerifiedAt,
      'type': type,
      'settings': settings,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        emailVerifiedAt,
        phone,
        phoneVerifiedAt,
        type,
        settings,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}
