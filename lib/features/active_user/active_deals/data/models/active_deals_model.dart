import '../../domain/entities/active_deals.dart';

class ActiveDealsModel extends ActiveDeals {
  const ActiveDealsModel({
    required int id,
    required int activeId,
    required int passiveId,
    required String title,
    String? description,
    required String status,
    required String type,
    Map<String, dynamic>? metadata,
    required String createdAt,
    required String updatedAt,
    String? deletedAt,
    required int messagesCount,
    ActiveUser? active,
  }) : super(
         id: id,
         activeId: activeId,
         passiveId: passiveId,
         title: title,
         description: description,
         status: status,
         type: type,
         metadata: metadata,
         createdAt: createdAt,
         updatedAt: updatedAt,
         deletedAt: deletedAt,
         messagesCount: messagesCount,
         active: active,
       );

  factory ActiveDealsModel.fromJson(Map<String, dynamic> json) {
    return ActiveDealsModel(
      id: json['id'] as int,
      activeId: json['active_id'] as int,
      passiveId: json['passive_id'] ?? 0,
      title: json['title'] as String,
      description: json['description'] as String?,
      status: json['status'] as String,
      type: json['type'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      deletedAt: json['deleted_at'] as String?,
      messagesCount: json['messages_count'] as int,
      active: json['active'] != null
          ? ActiveUserModel.fromJson(json['active'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'active_id': activeId,
      'passive_id': passiveId,
      'title': title,
      'description': description,
      'status': status,
      'type': type,
      'metadata': metadata,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'messages_count': messagesCount,
      'active': active != null ? (active as ActiveUserModel).toJson() : null,
    };
  }

  factory ActiveDealsModel.fromEntity(ActiveDeals entity) {
    return ActiveDealsModel(
      id: entity.id,
      activeId: entity.activeId,
      passiveId: entity.passiveId,
      title: entity.title,
      description: entity.description,
      status: entity.status,
      type: entity.type,
      metadata: entity.metadata,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      messagesCount: entity.messagesCount,
      active: entity.active,
    );
  }
}

class ActiveUserModel extends ActiveUser {
  const ActiveUserModel({
    required int id,
    required String name,
    required String email,
    String? emailVerifiedAt,
    String? phone,
    String? phoneVerifiedAt,
    required String type,
    Map<String, dynamic>? settings,
    required String createdAt,
    required String updatedAt,
    String? deletedAt,
  }) : super(
         id: id,
         name: name,
         email: email,
         emailVerifiedAt: emailVerifiedAt,
         phone: phone,
         phoneVerifiedAt: phoneVerifiedAt,
         type: type,
         settings: settings,
         createdAt: createdAt,
         updatedAt: updatedAt,
         deletedAt: deletedAt,
       );

  factory ActiveUserModel.fromJson(Map<String, dynamic> json) {
    return ActiveUserModel(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      emailVerifiedAt: json['email_verified_at'] as String?,
      phone: json['phone'] as String?,
      phoneVerifiedAt: json['phone_verified_at'] as String?,
      type: json['type'] as String,
      settings: json['settings'] as Map<String, dynamic>?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      deletedAt: json['deleted_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'email_verified_at': emailVerifiedAt,
      'phone': phone,
      'phone_verified_at': phoneVerifiedAt,
      'type': type,
      'settings': settings,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }
}
