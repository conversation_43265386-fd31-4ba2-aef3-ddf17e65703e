import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flipped_flutter/core/network/dio_client.dart';
import 'package:http/http.dart' as http;
import '../../../../../config/app_config.dart';
import '../../../../../core/error/exceptions.dart';
import '../../../../../core/model/api_response.dart';
import '../../domain/usecases/get_active_deals_usecase.dart';
import '../models/active_deals_model.dart';

abstract class ActiveDealsRemoteDataSource {
  Future<List<ActiveDealsModel>> getActiveDeals(GetActiveDealsParams params);
}

class ActiveDealsRemoteDataSourceImpl implements ActiveDealsRemoteDataSource {
  final DioClient networkClient;

  ActiveDealsRemoteDataSourceImpl({required this.networkClient});

  @override
  Future<List<ActiveDealsModel>> getActiveDeals(
    GetActiveDealsParams params,
  ) async {
    try {
      final response = await networkClient.client.get(
        '/active/deals',
        queryParameters: params.toQueryParams(),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data')) {
          final List<dynamic> dataList = responseData['data'] as List<dynamic>;

          return dataList
              .map(
                (item) =>
                    ActiveDealsModel.fromJson(item as Map<String, dynamic>),
              )
              .toList();
        } else {
          throw ServerException('Invalid response format');
        }
      } else {
        throw ServerException(
          'Failed to load deals',
          statusCode: response.statusCode,
        );
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Helper method to handle API responses with proper error handling
  T _handleApiResponse<T>(
    Response response,
    T Function(dynamic) fromJson,
    String errorMessage,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      try {
        return fromJson(response.data);
      } catch (e) {
        throw ServerException('Failed to parse response: $e');
      }
    } else {
      throw ServerException(errorMessage, statusCode: response.statusCode);
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException('Connection timeout');
      case DioExceptionType.connectionError:
        return NetworkException('No internet connection');
      case DioExceptionType.badResponse:
        return ServerException(
          e.response?.data['message'] ?? 'Server error',
          statusCode: e.response?.statusCode,
        );

      default:
        return ServerException('Unexpected error occurred');
    }
  }
}
