import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/active_deals_model.dart';

abstract class ActiveDealsLocalDataSource {
  Future<List<ActiveDealsModel>> getCachedActiveDeals();
  Future<void> cacheActiveDeals(List<ActiveDealsModel> activeDealsToCache);
}

class ActiveDealsLocalDataSourceImpl implements ActiveDealsLocalDataSource {
  final SharedPreferences sharedPreferences;

  ActiveDealsLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<ActiveDealsModel>> getCachedActiveDeals() {
    final jsonString = sharedPreferences.getString('CACHED_ACTIVE_DEALS');
    if (jsonString != null) {
      return Future.value(
        (json.decode(jsonString) as List)
            .map((item) => ActiveDealsModel.fromJson(item))
            .toList(),
      );
    } else {
      throw Exception('No cached active_deals found');
    }
  }

  @override
  Future<void> cacheActiveDeals(List<ActiveDealsModel> activeDealsToCache) {
    final List<Map<String, dynamic>> jsonList =
        activeDealsToCache.map((deal) => deal.toJson()).toList();
    return sharedPreferences.setString(
      'CACHED_ACTIVE_DEALS',
      json.encode(jsonList),
    );
  }
}
