import 'package:dartz/dartz.dart';
import '../../../../../core/error/exceptions.dart';
import '../../../../../core/error/failures.dart';
import '../../../../../core/network/network_info.dart';

import '../../domain/entities/active_deals.dart';
import '../../domain/repositories/active_deals_repository.dart';
import '../datasources/active_deals_local_data_source.dart';
import '../datasources/active_deals_remote_data_source.dart';

class ActiveDealsRepositoryImpl implements ActiveDealsRepository {
  final ActiveDealsRemoteDataSource remoteDataSource;
  final ActiveDealsLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  ActiveDealsRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<ActiveDeals>>> getActiveDeals() async {
    if (await networkInfo.isConnected) {
      try {
        final remoteActiveDeals = await remoteDataSource.getActiveDeals();
        localDataSource.cacheActiveDeals(remoteActiveDeals);
        return Right(remoteActiveDeals);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      }
    } else {
      try {
        final localActiveDeals = await localDataSource.getCachedActiveDeals();
        return Right(localActiveDeals);
      } on CacheException catch (e) {
        return Left(CacheFailure(e.message));
      }
    }
  }
}
