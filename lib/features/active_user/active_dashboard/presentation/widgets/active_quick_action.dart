import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../../core/navigation/routing/route_names.dart';
import '../../../../../shared/constants/app_colors.dart';

class ActiveQuickAction extends StatelessWidget {
  const ActiveQuickAction({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // First Button (Default)
            Expanded(
              child: SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: () {
                    context.go(RouteNames.submit_deal);
                  },
                  child: const Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 8,
                    children: [Icon(CupertinoIcons.add), Text("Submit Deal")],
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12), // spacing between buttons
            // Second But<PERSON> (White with border)
            Expanded(
              child: SizedBox(
                height: 50,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Theme.of(context).colorScheme.onBackground,
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1.5,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  onPressed: () {},
                  child: const Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    spacing: 8,
                    children: [
                      Icon(
                        CupertinoIcons.calendar,
                        color: AppColors.colorPrimary,
                      ),
                      Text(
                        "Weekly Report",
                        style: TextStyle(color: AppColors.colorPrimary),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
