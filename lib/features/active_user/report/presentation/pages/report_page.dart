import 'package:flipped_flutter/shared/widgets/common/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../injection_container.dart';
import '../bloc/report_bloc.dart';
import '../widgets/report_widget.dart';

class ReportPage extends StatelessWidget {
  const ReportPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: "Reports"),
      body: BlocProvider(
        create: (_) => sl<ReportBloc>(),
        child: const ReportWidget(),
      ),
    );
  }
}
