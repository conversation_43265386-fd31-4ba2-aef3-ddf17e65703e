import 'package:flutter/material.dart';

import '../../../../../shared/widgets/common/elavated_container.dart';

// Component 1: Create Weekly Report Card
class CreateWeeklyReportCard extends StatelessWidget {
  final VoidCallback? onCreateReport;

  const CreateWeeklyReportCard({
    Key? key,
    this.onCreateReport,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      padding: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.add,
                  color: Color(0xFF0E7490),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Create Weekly Report',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF0E7490),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              'Create a new weekly report for your active projects',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onCreateReport,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(0xFF0891B2),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.description, color: Colors.white),
                    const SizedBox(width: 12),
                    Text(
                      'New Weekly Report',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}