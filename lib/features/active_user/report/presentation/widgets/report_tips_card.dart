import 'package:flutter/material.dart';

import '../../../../../shared/widgets/common/elavated_container.dart';

class ReportTipsCard extends StatelessWidget {
  final List<String> tips;

  const ReportTipsCard({
    Key? key,
    this.tips = const [
      'Include photos of work completed',
      'Document any issues or delays',
      'Submit by Monday for the previous week',
      'Add videos for major milestones',
    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      padding: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Report Tips',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Color(0xFF0E7490),
              ),
            ),
            const SizedBox(height: 20),
            ...tips.map((tip) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: Color(0xFF0E7490),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      tip,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }
}