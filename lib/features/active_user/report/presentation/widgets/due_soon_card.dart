import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';

class DueSoonCard extends StatelessWidget {
  final List<ProjectReport> reports;

  const DueSoonCard({
    Key? key,
    required this.reports,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      padding: const EdgeInsets.all(16),

      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: Color(0xFF0E7490),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Due Soon',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF0E7490),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            ...reports.map((report) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _ProjectReportItem(report: report),
            )).toList(),
          ],
        ),
      ),
    );
  }
}

class _ProjectReportItem extends StatelessWidget {
  final ProjectReport report;

  const _ProjectReportItem({
    Key? key,
    required this.report,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(0xFFFFF7ED),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Color(0xFFFED7AA),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  report.projectName,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Week ending ${report.weekEnding}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Due in ${report.daysRemaining} days',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFFEA580C),
              ),
            ),
          ),
        ],
      ),
    );
  }
}




class ProjectReport {
  final String projectName;
  final String weekEnding;
  final int daysRemaining;

  ProjectReport({
    required this.projectName,
    required this.weekEnding,
    required this.daysRemaining,
  });

  // Demo data
  static List<ProjectReport> getDemoData() {
    return [
      ProjectReport(
        projectName: '789 Pine Street',
        weekEnding: '28/01/2024',
        daysRemaining: 2,
      ),
      ProjectReport(
        projectName: '321 Elm Road',
        weekEnding: '28/01/2024',
        daysRemaining: 2,
      ),
      ProjectReport(
        projectName: '456 Oak Avenue',
        weekEnding: '04/02/2024',
        daysRemaining: 5,
      ),
      ProjectReport(
        projectName: '123 Main Street',
        weekEnding: '11/02/2024',
        daysRemaining: 12,
      ),
    ];
  }
}
