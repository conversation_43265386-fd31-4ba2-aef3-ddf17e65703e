import 'package:flipped_flutter/features/active_user/report/presentation/widgets/report_tips_card.dart';
import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flipped_flutter/shared/widgets/common/error_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/report_bloc.dart';
import 'create_weekly_report_card.dart';
import 'due_soon_card.dart';

class ReportWidget extends StatefulWidget {
  const ReportWidget({Key? key}) : super(key: key);

  @override
  State<ReportWidget> createState() => _ReportWidgetState();
}

class _ReportWidgetState extends State<ReportWidget> {
  @override
  void initState() {
    super.initState();
    // Trigger the event to load data when the widget initializes
    if(    context.read<ReportBloc>().state is ReportInitial){
      context.read<ReportBloc>().add(GetReportEvent());
    }
  }

  @override
  Widget build(BuildContext context) {

    final reports = [
      ProjectReport(
        projectName: '789 Pine Street',
        weekEnding: '28/01/2024',
        daysRemaining: 2,
      ),
      ProjectReport(
        projectName: '321 Elm Road',
        weekEnding: '28/01/2024',
        daysRemaining: 2,
      ),
    ];


    return BlocBuilder<ReportBloc, ReportState>(
      builder: (context, state) {
        if (state is ReportInitial) {
          return const Center(
            child: Text('Press the button to load report'),
          );
        } else if (state is ReportLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else if (state is ReportLoaded) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppValues.padding),
              child: Column(
                children: [
                  CreateWeeklyReportCard(
                    onCreateReport: () {
                    },
                  ),
                  SizedBox(height: AppValues.height_10,),
                  DueSoonCard(reports: reports),
                  SizedBox(height: AppValues.height_10,),
                  ReportTipsCard(),
                ],
              ),
            ),
          );
        } else if (state is ReportError) {
          return ErrorWidgetCustom(message: '${state.message}');
        } else {
          return const Center(
            child: Text('Unknown state'),
          );
        }
      },
    );
  }
}