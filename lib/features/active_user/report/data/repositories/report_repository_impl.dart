import 'package:dartz/dartz.dart';
import '../../../../../core/error/exceptions.dart';
import '../../../../../core/error/failures.dart';
import '../../../../../core/network/network_info.dart';
import '../../domain/entities/report.dart';
import '../../domain/repositories/report_repository.dart';
import '../datasources/report_local_data_source.dart';
import '../datasources/report_remote_data_source.dart';

class ReportRepositoryImpl implements ReportRepository {
  final ReportRemoteDataSource remoteDataSource;
  final ReportLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  ReportRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, Report>> getReport() async {
    final stopwatch = Stopwatch()..start();

    try {
      // Check connectivity with timeout
      final connectivityCheckStart = DateTime.now();
      final isConnected = await networkInfo.isConnected;
      final connectivityCheckDuration = DateTime.now().difference(connectivityCheckStart);

      print('🌐 Connectivity check took: ${connectivityCheckDuration.inMilliseconds}ms');

      if (isConnected) {
        try {
          final apiCallStart = DateTime.now();
          final remoteReport = await remoteDataSource.getReport();
          final apiCallDuration = DateTime.now().difference(apiCallStart);

          print('📡 API call took: ${apiCallDuration.inMilliseconds}ms');

          localDataSource.cacheReport(remoteReport);
          return Right(remoteReport);
        } on ServerException catch (e) {
          print('❌ Server error: ${e.message}');
          // Try to return cached data as fallback
          try {
            final localReport = await localDataSource.getCachedReport();
            print('📱 Returning cached data as fallback');
            return Right(localReport);
          } on CacheException {
            return Left(ServerFailure(e.message));
          }
        }
      } else {
        try {
          final localReport = await localDataSource.getCachedReport();
          return Right(localReport);
        } on CacheException catch (e) {
          return Left(CacheFailure(e.message));
        }
      }
    } finally {
      stopwatch.stop();
      print('⏱️ Total getReport() took: ${stopwatch.elapsedMilliseconds}ms');
    }
  }
}
