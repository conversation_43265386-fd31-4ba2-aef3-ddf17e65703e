import 'package:flipped_flutter/shared/constants/app_colors.dart';
import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';
import '../../../../../shared/utils/styles/app_text_styles.dart';

class NotificationCard extends StatelessWidget {
  const NotificationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      child: Padding(
        padding: const EdgeInsets.all(AppValues.halfPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Notifications", style: AppTextStyles.primaryTitle(context)),
            ListTile(
              leading: Icon(
                Icons.notifications_outlined,
                color: AppColors.iconPrimary,
              ),
              title: Text('Deal Updates'),
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            Divider(color: Colors.grey[300]),
            ListTile(
              leading: Icon(
                Icons.notifications_outlined,
                color: AppColors.iconPrimary,
              ),
              title: Text('Invoice Reminders'),
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            Divider(color: Colors.grey[300]),
            ListTile(
              leading: Icon(
                Icons.notifications_outlined,
                color: AppColors.iconPrimary,
              ),
              title: Text('Report Reminders'),
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
            Divider(color: Colors.grey[300]),
            ListTile(
              leading: Icon(
                Icons.notifications_outlined,
                color: AppColors.iconPrimary,
              ),
              title: Text('Chat Messages'),
              trailing: Switch(value: true, onChanged: (value) {}),
            ),
          ],
        ),
      ),
    );
  }
}
