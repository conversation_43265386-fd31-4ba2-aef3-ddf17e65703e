import 'package:flipped_flutter/shared/constants/app_colors.dart';
import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flutter/material.dart';
import '../../../../../shared/utils/styles/app_text_styles.dart';
import '../../../../../shared/widgets/common/elavated_container.dart';

class ActiveVerificationCard extends StatelessWidget {
  const ActiveVerificationCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      child: Padding(
        padding: const EdgeInsets.all(AppValues.halfPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Verification Status",
              style: AppTextStyles.primaryTitle(context),
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(" Identity Verification"),
                Card(

                  color: AppColors.colorPrimary,
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Text("Verified",style: TextStyle(color: AppColors.colorWhite),),
                    )),
              ],
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(" Bank Details"),
                Card(
                    color: AppColors.colorPrimary,
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Text(" Virified",style: TextStyle(color: AppColors.colorWhite),),
                    )),
              ],
            ),

            Container(),
          ],
        ),
      ),
    );
  }
}
