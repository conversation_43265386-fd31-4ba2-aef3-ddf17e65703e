import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';
import '../../../../../shared/constants/app_colors.dart';
import '../../../../../shared/utils/styles/app_text_styles.dart';

class LegalSupport extends StatelessWidget {
  const LegalSupport({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Legal Support", style: AppTextStyles.primaryTitle(context)),

            ListTile(
              leading: Icon(Icons.event_note_outlined,color: AppColors.iconPrimary,),
              title: Text('Legal Support'),
              trailing: Icon(Icons.keyboard_arrow_right_outlined),
            ),
            Divider(color: Colors.grey[300]),
            ListTile(
              leading: Icon(Icons.privacy_tip,color: AppColors.iconPrimary,),
              title: Text('Privacy Policy'),
              trailing: Icon(Icons.keyboard_arrow_right_outlined),
            ),

            Divider(color: Colors.grey[300]),
            ListTile(
              leading: Icon(Icons.support_agent),
              title: Text('Support'),
              trailing: Icon(Icons.keyboard_arrow_right_outlined,color: AppColors.iconPrimary,),
            ),
          ],
        ),
      ),
    );
  }
}
