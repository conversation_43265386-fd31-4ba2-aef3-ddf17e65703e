import 'package:flipped_flutter/shared/constants/app_values.dart';
import 'package:flipped_flutter/shared/widgets/common/elavated_container.dart';
import 'package:flutter/material.dart';
import '../../../../../shared/constants/app_colors.dart';
import '../../../../../shared/utils/styles/app_text_styles.dart';

class ActiveAccountCard extends StatelessWidget {
  const ActiveAccountCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedContainer(
      child: Padding(
        padding: const EdgeInsets.all(AppValues.halfPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Account", style: AppTextStyles.primaryTitle(context)),
            const ListTile(
              leading: Icon(Icons.person,color: AppColors.iconPrimary,),
              title: Text('Personal Information'),
              trailing: Icon(Icons.keyboard_arrow_right_outlined),
            ),
            Divider(color: Colors.grey[300]),
            const ListTile(
              leading: Icon(Icons.security,color: AppColors.iconPrimary,),
              title: Text('Security Settings'),
              trailing: Icon(Icons.keyboard_arrow_right_outlined),
            ),
          ],
        ),
      ),
    );
  }
}
