import 'package:flipped_flutter/core/navigation/routing/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../config/app_config.dart';
import '../../../config/flavor_config.dart';
import '../../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../../features/auth/presentation/bloc/auth_state.dart';
import '../../../shared/services/logger/app_logger.dart';

class AuthGuard {
  static String? checkAuth(BuildContext context, GoRouterState state) {
    final environment = AppConfig.environment;

    // Log navigation in development
    if (environment.enableLogging) {
      AppLogger.logNavigationEvent(
        state.fullPath ?? 'unknown',
        'Checking access',
      );
    }

    // Get auth state from BLoC
    final authBloc = context.read<AuthBloc>();
    final isAuthenticated = authBloc.state.status == AuthStatus.authenticated;
    final protectedRoutes = [
      RouteNames.active_dashboard,
      RouteNames.active_deals,
      RouteNames.passive_deals,
      RouteNames.dealDetail,
      RouteNames.active_profile,
      RouteNames.support,
      RouteNames.passive_profile,
      RouteNames.submit_deal,
    ];
    final publicRoutes = [
      RouteNames.login,
      RouteNames.register,
      RouteNames.forgetPassword,
      RouteNames.resetPassword,
      RouteNames.authCheck,
    ];

    // Add development routes to protected routes
    if (FlavorConfig.isDevelopment) {
      protectedRoutes.addAll(['/debug', '/dev-tools']);
    }

    // Add staging routes to protected routes
    if (FlavorConfig.isStaging) {
      protectedRoutes.addAll(['/staging-info']);
    }

    if (!isAuthenticated) {
      final isPublic = publicRoutes.any(
        (route) => state.uri.toString().startsWith(route),
      );
      if (isPublic) {
        return null;
      }
      AppLogger.warning(
        'Access denied to ${state.uri.toString()} – redirecting to login',
      );
      return RouteNames.login;
    }
    // If authenticated and trying to access login/register, redirect to dashboard
    if (isAuthenticated && publicRoutes.contains(state.uri.toString())) {
      AppLogger.info('Already authenticated – redirecting to dashboard');
      return RouteNames.active_dashboard;
    }

    return null; // Allow access
  }

  static bool _isProtectedRoute(String location, List<String> protectedRoutes) {
    return protectedRoutes.any((route) => location.startsWith(route));
  }
}
