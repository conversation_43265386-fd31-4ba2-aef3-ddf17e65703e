import 'package:flipped_flutter/core/navigation/routing/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../config/app_config.dart';
import '../../../config/flavor_config.dart';
import '../../../shared/services/logger/app_logger.dart';

class NavigationHelper {
  static void goToLogin(BuildContext context) {
    _logNavigation('login');
    context.goNamed(RouteNames.login);
  }

  static void goToRegister(BuildContext context) {
    _logNavigation('register');
    context.pushNamed(RouteNames.register);
  }

  static void goToForgotPassword(BuildContext context) {
    _logNavigation('forgotPassword');
    context.pushNamed(RouteNames.forgetPassword);
  }

  static void goToResetPassword(BuildContext context, {required String email}) {
    _logNavigation('resetPassword', params: {'email': email});
    context.pushNamed(
      RouteNames.resetPassword,
      queryParameters: {'email': email},
    );
  }

  static void goToActiveDashboard(BuildContext context) {
    _logNavigation('dashboard');
    context.goNamed(RouteNames.active_dashboard);
  }
  static void goToPassiveDashboard(BuildContext context) {
    _logNavigation('dashboard');
    context.goNamed(RouteNames.passive_dashboard);
  }

  // Development-only navigation
  static void goToDebug(BuildContext context) {
    if (FlavorConfig.isDevelopment) {
      _logNavigation('debug');
      context.goNamed('debug');
    }
  }

  static void goToROICalculator(BuildContext context) {
    _logNavigation('roiCalculator');
    context.goNamed(RouteNames.roiCalculator);
  }

  static void goToDevTools(BuildContext context) {
    if (FlavorConfig.isDevelopment) {
      _logNavigation('devTools');
      context.goNamed('devTools');
    }
  }

  // Staging-only navigation
  static void goToStagingInfo(BuildContext context) {
    if (FlavorConfig.isStaging) {
      _logNavigation('stagingInfo');
      context.goNamed('stagingInfo');
    }
  }

  static void goBack(BuildContext context) {
    _logNavigation('back');
    if (context.canPop()) {
      context.pop();
    } else {
      context.goNamed('home');
    }
  }

  static void _logNavigation(String route, {Map<String, dynamic>? params}) {
    final environment = AppConfig.environment;
    if (environment.enableLogging) {
      AppLogger.info(
        '🧭 NavigationHelper: $route ${params != null ? 'with params: $params' : ''}',
      );
    }
  }
}
