import 'package:flipped_flutter/features/active_user/report/presentation/pages/report_page.dart';
import 'package:flipped_flutter/features/chat/presentation/pages/chat_page.dart';
import 'package:flipped_flutter/features/passive_user/passive_deals/presentation/pages/passive_deals_page.dart';
import 'package:flipped_flutter/features/passive_user/passive_profile/presentation/pages/roi_calcutalte_page.dart';
import 'package:flipped_flutter/features/passive_user/portfolio/presentation/pages/portfolio_page.dart';
import 'package:flipped_flutter/features/support/presentation/pages/support_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../../config/app_config.dart';
import '../../../config/flavor_config.dart';
import '../../../features/active_user/active_dashboard/presentation/pages/active_dashboard_page.dart';
import '../../../features/active_user/active_deals/presentation/pages/active_deals_page.dart';
import '../../../features/active_user/active_profile/presentation/pages/active_profile_page.dart';
import '../../../features/active_user/active_deals/presentation/pages/submit_deal_page.dart';
import '../../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../../features/auth/presentation/bloc/auth_state.dart';
import '../../../features/auth/presentation/pages/auth_check_page.dart';
import '../../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../../features/auth/presentation/pages/login_page.dart';
import '../../../features/auth/presentation/pages/register_page.dart';
import '../../../features/auth/presentation/pages/reset_password_page.dart';
import '../../../features/passive_user/passive_dashboard/presentation/pages/passive_dashboard_page.dart';
import '../../../features/passive_user/passive_profile/presentation/pages/passive_profile_page.dart';
import '../../../shared/page/debug_page.dart';
import '../../../shared/page/error_page.dart';
import '../shell_wrapper.dart';
import 'route_names.dart';
import 'route_guards.dart';

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<AuthState> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen((AuthState state) {
      notifyListeners();
    });
  }

  late final StreamSubscription<AuthState> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}

class AppRouter {
  static GoRouter createRouter(AuthBloc authBloc) {
    final environment = AppConfig.environment;

    return GoRouter(
      initialLocation: RouteNames.authCheck,
      debugLogDiagnostics: environment.enableLogging,
      redirect: AuthGuard.checkAuth,
      refreshListenable: GoRouterRefreshStream(authBloc.stream),
      errorBuilder: (context, state) => ErrorPage(
        error: state.error,
        showDebugInfo: environment.enableLogging,
      ),
      routes: _buildRoutes(),
    );
  }

  static List<RouteBase> _buildRoutes() {
    List<RouteBase> routes = [
      ShellRoute(
        pageBuilder: (context, state, child) {
          return _buildPageWithFadeTransition(
            state.pageKey,
            ShellWrapper(child: child),
          );
        },
        routes: [
          GoRoute(
            path: RouteNames.active_dashboard,
            name: RouteNames.active_dashboard,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              ActiveDashboardPage(),
            ),
          ),
          GoRoute(
            path: RouteNames.submit_deal,
            name: RouteNames.submit_deal,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              SubmitDealPage(),
            ),
          ),
          GoRoute(
            path: RouteNames.passive_dashboard,
            name: RouteNames.passive_dashboard,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              PassiveDashboardPage(),
            ),
          ),
          GoRoute(
            path: RouteNames.support,
            name: RouteNames.support,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, SupportPage()),
          ),

          GoRoute(
            path: RouteNames.active_profile,
            name: RouteNames.active_profile,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              ActiveProfilePage(),
            ),
          ),

          GoRoute(
            path: RouteNames.passive_profile,
            name: RouteNames.passive_profile,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              PassiveProfilePage(),
            ),
          ),

          GoRoute(
            path: RouteNames.active_deals,
            name: RouteNames.active_deals,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, ActiveDealsPage()),
          ),

          GoRoute(
            path: RouteNames.passive_deals,
            name: RouteNames.passive_deals,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, PassiveDealsPage()),
          ),

          GoRoute(
            path: RouteNames.chat,
            name: RouteNames.chat,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, ChatPage()),
          ),

          GoRoute(
            path: RouteNames.reports,
            name: RouteNames.reports,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, ReportPage()),
          ),

          GoRoute(
            path: RouteNames.portfolio,
            name: RouteNames.portfolio,
            pageBuilder: (context, state) =>
                _buildPageWithFadeTransition(state.pageKey, PortfolioPage()),
          ),

          GoRoute(
            path: RouteNames.roiCalculator,
            name: RouteNames.roiCalculator,
            pageBuilder: (context, state) => _buildPageWithFadeTransition(
              state.pageKey,
              ROICalculatorPage(),
            ),
          ),
        ],
      ),

      // Auth Routes
      GoRoute(
        path: RouteNames.login,
        name: RouteNames.login,
        builder: (context, state) => LoginPage(),
      ),
      GoRoute(
        path: RouteNames.register,
        name: RouteNames.register,
        builder: (context, state) => RegisterPage(),
      ),

      GoRoute(
        path: RouteNames.forgetPassword,
        name: RouteNames.forgetPassword,
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      GoRoute(
        path: RouteNames.resetPassword,
        name: RouteNames.resetPassword,
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          return ResetPasswordPage(email: email);
        },
      ),

      GoRoute(
        path: RouteNames.authCheck,
        name: RouteNames.authCheck,
        builder: (context, state) => const AuthCheckPage(),
      ),
    ];

    // Add development-only routes
    if (FlavorConfig.isDevelopment) {
      routes.addAll(_getDevelopmentRoutes());
    }

    // Add staging-specific routes
    if (FlavorConfig.isStaging) {
      routes.addAll(_getStagingRoutes());
    }

    return routes;
  }

  static List<RouteBase> _getDevelopmentRoutes() {
    return [
      GoRoute(
        path: '/debug',
        name: 'debug',
        builder: (context, state) => const DebugPage(),
      ),
    ];
  }

  static List<RouteBase> _getStagingRoutes() {
    return [];
  }

  static Page<dynamic> _buildPageWithFadeTransition(
    LocalKey key,
    Widget child,
  ) {
    return CustomTransitionPage(
      key: key,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(parent: animation, curve: Curves.easeInOut),
          child: child,
        );
      },
    );
  }
}
