import 'package:flipped_flutter/core/navigation/routing/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/presentation/bloc/auth_state.dart';
import 'bloc/navigation_bloc.dart';
import 'model/bottom_nav_item.dart';

class ShellWrapper extends StatefulWidget {
  final Widget child;

  const ShellWrapper({Key? key, required this.child}) : super(key: key);

  @override
  State<ShellWrapper> createState() => _ShellWrapperState();
}

class _ShellWrapperState extends State<ShellWrapper> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        final userType = authState.user?.type ?? '';

        return BlocBuilder<NavigationBloc, NavigationState>(
          builder: (context, navState) {
            return Scaffold(
              body: widget.child,
              bottomNavigationBar: BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                currentIndex: _getCurrentIndex(context, userType),
                onTap: (index) => _onTabTapped(context, index, userType),
                items: (userType == 'active'
                    ? ActiveUserBottomNavItems.items
                    : PassiveUserBottomNavItems.items)
                    .map(
                      (item) => BottomNavigationBarItem(
                    icon: Icon(item.icon),
                    activeIcon: Icon(item.activeIcon),
                    label: item.label,
                  ),
                )
                    .toList(),
              ),
            );
          },
        );
      },
    );
  }

  int _getCurrentIndex(BuildContext context, String userType) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith(RouteNames.active_dashboard) || location.startsWith(RouteNames.passive_dashboard)) return 0;
    if (location.startsWith(RouteNames.active_deals) || location.startsWith(RouteNames.passive_deals)) return 1;
    if (location.startsWith(RouteNames.reports) || location.startsWith(RouteNames.portfolio)) return 2;
    if (location.startsWith(RouteNames.chat) || location.startsWith(RouteNames.passive_profile)) return 3;
    if (location.startsWith(RouteNames.active_profile)) return 4;

    return 0;
  }

  void _onTabTapped(BuildContext context, int index, String userType) {
    context.read<NavigationBloc>().add(TabChanged(index));

    switch (index) {
      case 0:
        context.go(userType == 'active'
            ? RouteNames.active_dashboard
            : RouteNames.passive_dashboard);
        break;
      case 1:
        context.go(userType == 'active'
            ? RouteNames.active_deals
            : RouteNames.passive_deals);
        break;
      case 2:
        context.go(userType == 'active'
            ? RouteNames.reports
            : RouteNames.portfolio);
        break;
      case 3:
        context.go(userType == 'active'
            ? RouteNames.chat
            : RouteNames.passive_profile);
        break;
      case 4:
        if (userType == 'active') context.go(RouteNames.active_profile);
        break;
    }
  }
}