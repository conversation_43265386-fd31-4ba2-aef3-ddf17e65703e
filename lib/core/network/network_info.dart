import 'package:internet_connection_checker/internet_connection_checker.dart';

/// Abstract class for checking network connectivity
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// Concrete implementation of NetworkInfo using internet_connection_checker package
class NetworkInfoImpl implements NetworkInfo {
  final InternetConnectionChecker connectionChecker;
  bool? _cachedConnectionStatus;
  DateTime? _lastCheckTime;
  static const Duration _cacheTimeout = Duration(seconds: 10);

  NetworkInfoImpl(this.connectionChecker) {
    // Configure the connection checker with faster timeouts
    connectionChecker.checkTimeout = const Duration(seconds: 2);
    connectionChecker.checkInterval = const Duration(seconds: 1);
  }

  @override
  Future<bool> get isConnected async {
    final now = DateTime.now();

    // Return cached result if it's still valid
    if (_cachedConnectionStatus != null &&
        _lastCheckTime != null &&
        now.difference(_lastCheckTime!) < _cacheTimeout) {
      return _cachedConnectionStatus!;
    }

    try {
      // Use a timeout to prevent long delays
      final hasConnection = await connectionChecker.hasConnection
          .timeout(const Duration(seconds: 3));

      _cachedConnectionStatus = hasConnection;
      _lastCheckTime = now;

      return hasConnection;
    } catch (e) {
      // If connectivity check fails or times out, assume we have connection
      // and let the actual API call handle any network errors
      print('Network connectivity check failed: $e');
      _cachedConnectionStatus = true;
      _lastCheckTime = now;
      return true;
    }
  }

  /// Force refresh the connectivity status
  void refreshConnectivityStatus() {
    _cachedConnectionStatus = null;
    _lastCheckTime = null;
  }
}