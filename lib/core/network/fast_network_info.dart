import 'dart:io';
import 'network_info.dart';

/// A faster NetworkInfo implementation that uses a simple socket connection test
/// This is more suitable for development when you want faster responses
class FastNetworkInfo implements NetworkInfo {
  bool? _cachedConnectionStatus;
  DateTime? _lastCheckTime;
  static const Duration _cacheTimeout = Duration(seconds: 5);

  @override
  Future<bool> get isConnected async {
    final now = DateTime.now();
    
    // Return cached result if it's still valid
    if (_cachedConnectionStatus != null && 
        _lastCheckTime != null && 
        now.difference(_lastCheckTime!) < _cacheTimeout) {
      return _cachedConnectionStatus!;
    }

    try {
      // Quick socket test to Google DNS (very fast)
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 1));
      
      final hasConnection = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      _cachedConnectionStatus = hasConnection;
      _lastCheckTime = now;
      
      return hasConnection;
    } catch (e) {
      // If lookup fails, assume we have connection and let API handle errors
      print('Fast network check failed: $e');
      _cachedConnectionStatus = true;
      _lastCheckTime = now;
      return true;
    }
  }

  /// Force refresh the connectivity status
  void refreshConnectivityStatus() {
    _cachedConnectionStatus = null;
    _lastCheckTime = null;
  }
}
