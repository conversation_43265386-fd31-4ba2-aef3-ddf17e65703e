import 'package:dio/dio.dart';

import '../../shared/services/storage/secure_storage_service.dart';

class ApiInterceptor extends Interceptor {
  final SecureStorageService storage;

  ApiInterceptor({required this.storage});

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final token = await storage.getAccessToken()
          .timeout(const Duration(seconds: 2));
      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      print('Warning: Failed to get access token in ApiInterceptor: $e');
      // Continue without token
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Global error transformation
    handler.next(err);
  }
}
