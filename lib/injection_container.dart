import 'package:flipped_flutter/core/network/network_info.dart';
import 'package:flipped_flutter/features/active_user/report/data/datasources/report_local_data_source.dart';
import 'package:flipped_flutter/features/active_user/report/data/datasources/report_remote_data_source.dart';
import 'package:flipped_flutter/features/active_user/report/data/repositories/report_repository_impl.dart';
import 'package:flipped_flutter/features/active_user/report/domain/repositories/report_repository.dart';
import 'package:flipped_flutter/features/active_user/report/domain/usecases/get_report_usecase.dart';
import 'package:flipped_flutter/features/active_user/report/presentation/bloc/report_bloc.dart';
import 'package:flipped_flutter/features/auth/domain/usecases/forget_password_usecase.dart';
import 'package:flipped_flutter/features/auth/domain/usecases/reset_password_usecase.dart';
import 'package:flipped_flutter/shared/services/storage/secure_storage_service.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'config/app_config.dart';
import 'core/navigation/bloc/navigation_bloc.dart';
import 'core/network/dio_client.dart';
import 'core/network/fast_network_info.dart';
import 'core/network/network_info.dart';
import 'core/offline/database/local_data_base.dart';
import 'features/active_user/active_dashboard/data/datasources/active_dashboard_local_data_source.dart';
import 'features/active_user/active_dashboard/data/datasources/active_dashboard_remote_data_source.dart';
import 'features/active_user/active_dashboard/data/repositories/active_dashboard_repository_impl.dart';
import 'features/active_user/active_dashboard/domain/repositories/active_dashboard_repository.dart';
import 'features/active_user/active_dashboard/domain/usecases/get_active_dashboard_usecase.dart';
import 'features/active_user/active_dashboard/presentation/bloc/active_dashboard_bloc.dart';
import 'features/active_user/active_profile/data/datasources/active_profile_local_data_source.dart';
import 'features/active_user/active_profile/data/datasources/active_profile_remote_data_source.dart';
import 'features/active_user/active_profile/data/repositories/active_profile_repository_impl.dart';
import 'features/active_user/active_profile/domain/repositories/active_profile_repository.dart';
import 'features/active_user/active_profile/domain/usecases/get_active_profile_usecase.dart';
import 'features/active_user/active_profile/presentation/bloc/active_profile_bloc.dart';
import 'features/auth/data/datasources/auth_remote_datasource.dart';
import 'features/auth/data/repositories/auth_repository_impl.dart';
import 'features/auth/domain/repositories/auth_repository.dart';
import 'features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'features/auth/domain/usecases/get_current_user_usecase.dart';
import 'features/auth/domain/usecases/login_usecase.dart';
import 'features/auth/domain/usecases/logout_usecase.dart';
import 'features/auth/domain/usecases/register_usecase.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/chat/data/datasources/chat_local_data_source.dart';
import 'features/chat/data/datasources/chat_remote_data_source.dart';
import 'features/chat/data/repositories/chat_repository_impl.dart';
import 'features/chat/domain/repositories/chat_repository.dart';
import 'features/chat/domain/usecases/get_chat_usecase.dart';
import 'features/chat/presentation/bloc/chat_bloc.dart';
import 'features/passive_user/passive_dashboard/data/datasources/passive_dashboard_local_data_source.dart';
import 'features/passive_user/passive_dashboard/data/datasources/passive_dashboard_remote_data_source.dart';
import 'features/passive_user/passive_dashboard/data/repositories/passive_dashboard_repository_impl.dart';
import 'features/passive_user/passive_dashboard/domain/repositories/passive_dashboard_repository.dart';
import 'features/passive_user/passive_dashboard/domain/usecases/get_passive_dashboard_usecase.dart';
import 'features/passive_user/passive_dashboard/presentation/bloc/passive_dashboard_bloc.dart';
import 'features/passive_user/passive_profile/data/datasources/passive_profile_local_data_source.dart';
import 'features/passive_user/passive_profile/data/datasources/passive_profile_remote_data_source.dart';
import 'features/passive_user/passive_profile/data/repositories/passive_profile_repository_impl.dart';
import 'features/passive_user/passive_profile/domain/repositories/passive_profile_repository.dart';
import 'features/passive_user/passive_profile/domain/usecases/get_passive_profile_usecase.dart';
import 'features/passive_user/passive_profile/presentation/bloc/passive_profile_bloc.dart';
import 'features/passive_user/portfolio/data/datasources/portfolio_local_data_source.dart';
import 'features/passive_user/portfolio/data/datasources/portfolio_remote_data_source.dart';
import 'features/passive_user/portfolio/data/repositories/portfolio_repository_impl.dart';
import 'features/passive_user/portfolio/domain/repositories/portfolio_repository.dart';
import 'features/passive_user/portfolio/domain/usecases/get_portfolio_usecase.dart';
import 'features/passive_user/portfolio/presentation/bloc/portfolio_bloc.dart';

final sl = GetIt.instance;

Future<void> init(AppEnvironment environment) async {
  await _initCore(environment);
  await _initAuth();
  await _navigation();
  await _initProfile();
  await _initReport();
  await _initChat();
  await _initDashboard();
  await _initPortfolio();
}

Future<void> _navigation() async {
  sl.registerLazySingleton(() => NavigationBloc());
}

Future<void> _initCore(AppEnvironment environment) async {
  // Register SecureStorageService (used by DioClient and others)
  sl.registerLazySingleton<SecureStorageService>(() => SecureStorageService());

  // Register DioClient with SecureStorageService
  sl.registerLazySingleton<DioClient>(
    () => DioClient(storage: sl(), environment: environment),
  );

  //Register DatabaseService
  sl.registerLazySingleton<DatabaseService>(() => DatabaseService());

  // Use FastNetworkInfo for development, NetworkInfoImpl for production
  if (environment.appName.contains('Dev')) {
    sl.registerLazySingleton<NetworkInfo>(
      () => FastNetworkInfo(),
    );
  } else {
    // Configure InternetConnectionChecker with optimized settings for production
    final connectionChecker = InternetConnectionChecker.instance;
    connectionChecker.checkTimeout = const Duration(seconds: 2);
    connectionChecker.checkInterval = const Duration(seconds: 1);

    sl.registerLazySingleton<NetworkInfo>(
      () => NetworkInfoImpl(connectionChecker),
    );
  }


}

Future<void> _initDashboard() async {
//active dashboard

  sl.registerLazySingleton<ActiveDashboardRemoteDataSource>(
        () => ActiveDashboardRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );
  sl.registerLazySingleton<ActiveDashboardLocalDataSource>(
        () => ActiveDashboardLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<ActiveDashboardRepository>(
        () => ActiveDashboardRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetActiveDashboardUseCase(sl()));
  sl.registerLazySingleton(
        () => ActiveDashboardBloc(getActiveDashboardUseCase: sl()),
  );

//passive dashboard

  sl.registerLazySingleton<PassiveDashboardRemoteDataSource>(
        () => PassiveDashboardRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );
  sl.registerLazySingleton<PassiveDashboardLocalDataSource>(
        () => PassiveDashboardLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<PassiveDashboardRepository>(
        () => PassiveDashboardRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );
  sl.registerLazySingleton(() => GetPassiveDashboardUseCase(sl()));
  sl.registerLazySingleton(
        () => PassiveDashboardBloc(getPassiveDashboardUseCase: sl()),
  );

}




Future<void> _initPortfolio() async {
  sl.registerSingleton<PortfolioRemoteDataSource>(
    PortfolioRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );

  sl.registerLazySingleton<PortfolioLocalDataSource>(
        () => PortfolioLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<PortfolioRepository>(
        () => PortfolioRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetPortfolioUseCase(sl()));
  sl.registerLazySingleton(() => PortfolioBloc(getPortfolioUseCase: sl()));
}


Future<void> _initReport() async {
  sl.registerSingleton<ReportRemoteDataSource>(
    ReportRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );

  sl.registerLazySingleton<ReportLocalDataSource>(
    () => ReportLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<ReportRepository>(
    () => ReportRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetReportUseCase(sl()));
  sl.registerLazySingleton(() => ReportBloc(getReportUseCase: sl()));
}


Future<void> _initChat() async {
  sl.registerSingleton<ChatRemoteDataSource>(
    ChatRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );

  sl.registerLazySingleton<ChatLocalDataSource>(
        () => ChatLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<ChatRepository>(
        () => ChatRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetChatUseCase(sl()));
  sl.registerLazySingleton(() => ChatBloc(getChatUseCase: sl()));
}




Future<void> _initProfile() async {

  //active profile
  sl.registerLazySingleton<ActiveProfileRemoteDataSource>(
    () => ActiveProfileRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );

  sl.registerLazySingleton<ActiveProfileLocalDataSource>(
    () => ActiveProfileLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<ActiveProfileRepository>(
    () => ActiveProfileRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetPassiveProfileUseCase(sl()));
  sl.registerLazySingleton(
    () => PassiveProfileBloc(getPassiveProfileUseCase: sl()),
  );

  //passive profile
  sl.registerLazySingleton<PassiveProfileRemoteDataSource>(
        () => PassiveProfileRemoteDataSourceImpl(networkClient: sl<DioClient>()),
  );

  sl.registerLazySingleton<PassiveProfileLocalDataSource>(
        () => PassiveProfileLocalDataSourceImpl(sharedPreferences: sl()),
  );

  sl.registerLazySingleton<PassiveProfileRepository>(
        () => PassiveProfileRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      networkInfo: sl(),
    ),
  );

  sl.registerLazySingleton(() => GetActiveProfileUseCase(sl()));
  sl.registerLazySingleton(
        () => ActiveProfileBloc(getActiveProfileUseCase: sl()),
  );


}

Future<void> _initAuth() async {
  // Data source
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      networkClient: sl<DioClient>(),
      secureStorage: sl<SecureStorageService>(),
    ),
  );

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(remoteDataSource: sl(), secureStorage: sl()),
  );

  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => RegisterUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => GetCurrentUserUseCase(sl()));
  sl.registerLazySingleton(() => CheckAuthStatusUseCase(sl()));
  sl.registerLazySingleton(() => ForgetPasswordUseCase(sl()));
  sl.registerLazySingleton(() => ResetPasswordUsecase(sl()));

  // Bloc
  sl.registerFactory(
    () => AuthBloc(
      checkAuthStatus: sl(),
      getCurrentUser: sl(),
      login: sl(),
      register: sl(),
      forgetPassword: sl(),
      resetPassword: sl(),
      logout: sl(),
    ),
  );
}
