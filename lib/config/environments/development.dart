import '../../shared/services/logger/log_level.dart';
import '../app_config.dart';

class DevelopmentEnvironment extends AppEnvironment {
  @override
  String get appName => 'Flipped Dev';

  @override
  String get baseUrl => 'https://wp.aminul.work/api/v1';

  @override
  String get socketUrl => 'http://*************:3000';

  @override
  String get apiKey => 'dev_api_key_here';

  @override
  bool get enableLogging => true;

  @override
  LogLevel get logLevel => LogLevel.debug;

  @override
  bool get enableCrashlytics => false;

  @override
  bool get enableAnalytics => false;

  @override
  String get bundleId => 'com.flipped.dev';

  @override
  int get connectTimeout => 10; // 10 seconds - reduced for faster failures

  @override
  int get receiveTimeout => 15; // 15 seconds - reduced for faster failures

  @override
  Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Api-Key': api<PERSON>ey,
  };
}
